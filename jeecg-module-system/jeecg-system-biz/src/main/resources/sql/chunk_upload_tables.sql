-- 分片上传相关表结构
-- 创建时间: 2025-08-04
-- 作者: <PERSON> (工程师)

-- 上传任务表
CREATE TABLE IF NOT EXISTS `upload_task` (
  `id` varchar(32) NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_size` bigint NOT NULL COMMENT '文件总大小(字节)',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件MD5哈希值',
  `chunk_size` int NOT NULL DEFAULT 5242880 COMMENT '分片大小(字节,默认5MB)',
  `total_chunks` int NOT NULL COMMENT '总分片数',
  `uploaded_chunks` int NOT NULL DEFAULT 0 COMMENT '已上传分片数',
  `status` varchar(20) NOT NULL DEFAULT 'UPLOADING' COMMENT '状态:UPLOADING,COMPLETED,FAILED,CANCELLED',
  `upload_type` varchar(20) DEFAULT 'local' COMMENT '上传类型:local,minio,alioss',
  `biz_path` varchar(255) DEFAULT NULL COMMENT '业务路径',
  `final_url` varchar(500) DEFAULT NULL COMMENT '最终文件URL',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `expired_time` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_expired_time` (`expired_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分片上传任务表';

-- 分片信息表
CREATE TABLE IF NOT EXISTS `upload_chunk` (
  `id` varchar(32) NOT NULL COMMENT '分片ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `chunk_index` int NOT NULL COMMENT '分片索引(从0开始)',
  `chunk_size` int NOT NULL COMMENT '分片实际大小(字节)',
  `chunk_hash` varchar(64) DEFAULT NULL COMMENT '分片MD5哈希值',
  `storage_path` varchar(500) DEFAULT NULL COMMENT '存储路径',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态:PENDING,UPLOADING,COMPLETED,FAILED',
  `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_chunk` (`task_id`, `chunk_index`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_chunk_task` FOREIGN KEY (`task_id`) REFERENCES `upload_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分片上传详情表';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_upload_task_status_time` ON `upload_task` (`status`, `created_time`);
CREATE INDEX IF NOT EXISTS `idx_upload_chunk_task_status` ON `upload_chunk` (`task_id`, `status`);

-- 插入初始化数据(如果需要)
-- INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) 
-- VALUES ('chunk_upload_status', '分片上传状态', 'chunk_upload_status', '分片上传任务状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 清理过期任务的存储过程(可选)
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanExpiredUploadTasks`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_id_var VARCHAR(32);
    DECLARE cur CURSOR FOR 
        SELECT id FROM upload_task 
        WHERE status IN ('FAILED', 'CANCELLED') 
        AND expired_time < NOW();
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO task_id_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 删除相关分片记录
        DELETE FROM upload_chunk WHERE task_id = task_id_var;
        -- 删除任务记录
        DELETE FROM upload_task WHERE id = task_id_var;
        
    END LOOP;
    
    CLOSE cur;
END$$
DELIMITER ;

-- 创建定时清理事件(可选，需要开启事件调度器)
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `evt_clean_expired_upload_tasks`
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   CALL CleanExpiredUploadTasks();
