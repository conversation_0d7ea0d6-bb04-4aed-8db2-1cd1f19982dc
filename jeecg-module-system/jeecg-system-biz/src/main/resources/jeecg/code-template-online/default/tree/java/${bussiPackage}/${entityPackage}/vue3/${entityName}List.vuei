<#include "/common/utils.ftl">
<#assign pidFieldName = "">
<#assign hasChildrenField = "">
<#assign bpm_flag=false>
<#list originalColumns as po>
  <#if po.fieldDbName == tableVo.extendParams.pidField>
    <#assign pidFieldName = po.fieldName>
  </#if>
  <#if po.fieldDbName == tableVo.extendParams.hasChildren>
    <#assign hasChildrenField = po.fieldName>
  </#if>
</#list>
<#assign list_need_pca=false>
<#-- 开始循环 -->
<#list columns as po>
  <#if po.fieldDbName=='bpm_status'>
    <#assign bpm_flag=true>
  </#if>
<#if po.classType=='pca'>
<#assign list_need_pca=true>
</#if>
</#list>
<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys" @expand="handleExpand" @fetch-success="onFetchSuccess">
      <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'${entityPackage}:${tableName}:add'" @click="handleCreate" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'${entityPackage}:${tableName}:exportXls'"  preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button  type="primary" v-auth="'${entityPackage}:${tableName}:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>

          <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button  v-auth="'${entityPackage}:${tableName}:deleteBatch'">批量操作
            <Icon icon="ant-design:down-outlined"></Icon>
          </a-button>
        </a-dropdown>
        <#-- update-begin---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
        <#-- update-end---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      <#list columns as po>
        <#if po.classType=='umeditor' || po.classType=='pca' || po.classType=='file'>
        <template v-if="column.dataIndex==='${po.fieldName}'">
        <#if po.classType=='umeditor'>
          <!--富文本件字段回显插槽-->
          <div v-html="text"></div>
        </#if>
        <#if po.classType=='pca'>
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </#if>
        <#if po.classType=='file'>
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">下载</a-button>
        </#if>
        </template>
        </#if>
      </#list>
      </template>
    </BasicTable>
    <!--字典弹窗-->
    <${entityName}Modal @register="registerModal" @success="handleSuccess"/>
  </div>
</template>

<script lang="ts" name="${entityPackage}-${entityName?uncap_first}" setup>
  //ts语法
  import {ref, reactive, computed, unref, toRaw, nextTick} from 'vue';
  import {BasicTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import ${entityName}Modal from './components/${entityName}Modal.vue';
  import {columns, searchFormSchema, superQuerySchema} from './${entityName}.data';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import {list, delete${entityName}, batchDelete${entityName}, getExportUrl,getImportUrl, getChildList,getChildListBatch} from './${entityName}.api';
  <#if list_need_pca>
  import { getAreaTextByCode } from '/@/components/Form/src/utils/Area';
  </#if>
  <#if bpm_flag==true>
  import { startProcess } from '/@/api/common/api';
  </#if>
  const queryParam = reactive<any>({});
  const expandedRowKeys = ref([]);
  //字典model
  const [registerModal, {openModal}] = useModal();
   //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
    tableProps:{
         api: list,
         title: '${tableVo.ftlDescription}',
         columns,
         canResize:false,
         <#-- update-begin---author:chenrui ---date:20231228  for：[issue/#5658] 树表复选框与展开按钮重叠问题---------- -->
         isTreeTable: true,
         <#-- update-end---author:chenrui ---date:20231228  for：[issue/#5658] 树表复选框与展开按钮重叠问题---------- -->
         formConfig: {
           //labelWidth: 120,
           schemas: searchFormSchema,
           autoSubmitOnEnter:true,
           showAdvancedButton:true,
           fieldMapToNumber: [
           <#list columns as po>
           <#if po.isQuery=='Y'>
           <#if po.queryMode!='single'>
              <#-- update-begin---author:chenrui ---date:20240527  for：[TV360X-388]时间范围查询控件---------- -->
              <#if po.fieldDbType=='int' || po.fieldDbType=='double' || po.fieldDbType=='BigDecimal' || po.classType=='time'>
              <#-- update-end---author:chenrui ---date:20240527  for：[TV360X-388]时间范围查询控件---------- -->
              ['${po.fieldName}', ['${po.fieldName}_begin', '${po.fieldName}_end']],
           </#if>
           </#if>
           </#if>
           </#list>
           ],
           fieldMapToTime: [
           <#list columns as po>
           <#if po.isQuery=='Y'>
           <#if po.queryMode!='single'>
           <#if po.classType=='date'>
              ['${po.fieldName}', ['${po.fieldName}_begin', '${po.fieldName}_end'], 'YYYY-MM-DD'],
           <#elseif po.classType=='datetime'>
              ['${po.fieldName}', ['${po.fieldName}_begin', '${po.fieldName}_end'], 'YYYY-MM-DD HH:mm:ss'],
           </#if>
           </#if>
           </#if>
           </#list>
           ],
         },
         actionColumn: {
           width: 240,
           fixed:'right'
         },
         beforeFetch: (params) => {
           params.hasQuery = "true"; 
           return Object.assign(params, queryParam);
         },
    },
     exportConfig: {
          name:"${tableVo.ftlDescription}",
          url: getExportUrl,
          params: queryParam,
        },
        importConfig: {
          url: getImportUrl,
          success: importSuccess
        },
    })

  const [registerTable, {reload, collapseAll, updateTableDataRecord, findTableDataRecord,getDataSource},{ rowSelection, selectedRowKeys }] = tableContext

<#-- update-begin---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
<#-- update-end---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 详情
   */
  async function handleDetail(record) {
    openModal(true, {
      record,
      isUpdate: true,
      hideFooter: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await delete${entityName}({id: record.id}, importSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    const ids = selectedRowKeys.value.filter(item => !item.includes('loadChild'))
    await batchDelete${entityName}({id: ids}, importSuccess);
  }
  /**
   * 导入
   */
   function importSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 添加下级
   */
  function handleAddSub(record) {
    openModal(true, {
      record,
      isUpdate: false,
    });
  }
  /**
   * 成功回调
   */
  async function handleSuccess({isUpdate, values, expandedArr, changeParent}) {
    if (isUpdate) {
      if (changeParent) {
        reload();
      } else {
        let data = await list({ id: values.id, pageSize: 1, pageNo: 1, ${pidFieldName}: values['${pidFieldName}'] });
        if (data && data.records && data.records.length > 0) {
          // 编辑回调
          updateTableDataRecord(values.id, data.records[0]);
        }else{
          updateTableDataRecord(values.id, values);
        }
      }
    } else {
        if(!values['id'] || !values['${pidFieldName}']){
            //新增根节点
            reload();
        }else{
            //新增子集
            expandedRowKeys.value = [];
            for (let key of unref(expandedArr)) {
                await expandTreeNode(key)
            }
        }
    }
  }

  /**
   * 接口请求成功后回调
   */
  function onFetchSuccess(result) {
      getDataByResult(result.items)&&loadDataByExpandedRows();
  }
  /**
   * 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
   */
  async function loadDataByExpandedRows() {
      if (unref(expandedRowKeys).length > 0) {
          const res = await getChildListBatch({ parentIds: unref(expandedRowKeys).join(',')});
          if (res.success && res.result.records.length>0) {
              //已展开的数据批量子节点
              let records = res.result.records
              const listMap = new Map();
              for (let item of records) {
                  let pid = item['${pidFieldName}'];
                  if (unref(expandedRowKeys).includes(pid)) {
                      let mapList = listMap.get(pid);
                      if (mapList == null) {
                          mapList = [];
                      }
                      mapList.push(item);
                      listMap.set(pid, mapList);
                  }
              }
              let childrenMap = listMap;
              let fn = (list) => {
                  if(list) {
                      list.forEach(data => {
                          if (unref(expandedRowKeys).includes(data.id)) {
                              data.children = getDataByResult(childrenMap.get(data.id))
                              fn(data.children)
                          }
                      })
                  }
              };
              fn(getDataSource())
          }
      }
  }
  /**
   * 处理数据集
   */
  function getDataByResult(result){
      if(result && result.length>0){
          return result.map(item=>{
              //判断是否标记了带有子节点
              if(item["hasChild"]=='1'){
                  let loadChild = { id: item.id+'_loadChild', name: 'loading...', isLoading: true }
                  item.children = [loadChild]
              }
              return item
          })
      }
  }
  /**
   *树节点展开合并
   * */
  async function handleExpand(expanded, record) {
    // 判断是否是展开状态，展开状态(expanded)并且存在子集(children)并且未加载过(isLoading)的就去查询子节点数据
    if (expanded) {
       expandedRowKeys.value.push(record.id)
      if (record.children.length > 0 && !!record.children[0].isLoading) {
        let result = await getChildList({${pidFieldName}: record.id});
        result=result.records?result.records:result;
        if (result && result.length > 0) {
          record.children = getDataByResult(result);
        } else {
          record.children = null
          record.hasChild = '0'
        }
      }
    } else {
      let keyIndex = expandedRowKeys.value.indexOf(record.id)
      if (keyIndex >= 0) {
        expandedRowKeys.value.splice(keyIndex, 1);
      }
    }
  }
  /**
   *操作表格后处理树节点展开合并
   * */
  async function expandTreeNode(key) {
    let record = findTableDataRecord(key)
    expandedRowKeys.value.push(key);
      let result = await getChildList({${pidFieldName}: key});
      if (result && result.length > 0) {
          record.children = getDataByResult(result);
      } else {
          record.children = null
          record.hasChild = '0'
      }
      updateTableDataRecord(key, record);
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: '${entityPackage}:${tableName}:edit'
      },
      {
        label: '添加下级',
        onClick: handleAddSub.bind(null, {${pidFieldName}: record.id}),
      }
    ]
  }
   /**
    * 下拉操作栏
    */
   function getDropDownAction(record){
     <#if bpm_flag==true>
     let dropDownAction = [
       {
         label: '详情',
         onClick: handleDetail.bind(null, record),
       }, {
         label: '删除',
         popConfirm: {
           title: '是否确认删除',
           confirm: handleDelete.bind(null, record),
           placement: 'topLeft'
         },
         auth: '${entityPackage}:${tableName}:delete'
     ];
     if(record.bpmStatus == '1' || !record.bpmStatus){
       dropDownAction.push({
         label: '发起流程',
         popConfirm: {
           title: '确认提交流程吗？',
           confirm: handleProcess.bind(null, record),
           placement: 'topLeft',
         }
       })
     }
     return dropDownAction;
     <#else>
     return [
       {
         label: '详情',
         onClick: handleDetail.bind(null, record),
       }, {
         label: '删除',
         popConfirm: {
           title: '确定删除吗?',
           confirm: handleDelete.bind(null, record),
           placement: 'topLeft'
         },
         auth: '${entityPackage}:${tableName}:delete'
       }
     ]
     </#if>
   }

  <#if bpm_flag==true>
  /**
   * 提交流程
   */
  async function handleProcess(record) {
    let params = {
      flowCode: 'dev_${tableName}_001',
      id: record.id,
      formUrl: '${entityPackagePath}/components/${entityName}Form',
      formUrlMobile: ''
    }
    await startProcess(params);
    await reload();
  }
  </#if>

</script>

<style lang="less" scoped>
<#include "/common/form/vue3SearchStyle.ftl">
</style>
