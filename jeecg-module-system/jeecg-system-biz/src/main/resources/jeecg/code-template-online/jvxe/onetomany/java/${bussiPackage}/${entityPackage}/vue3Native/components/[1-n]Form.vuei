<#include "/common/utils.ftl">
<#list subTables as sub>
<#if sub.foreignRelationType=='1'>
#segment#${sub.entityName}Form.vue
<#include "/common/utils.ftl">
<#assign need_category = false>
<#assign bpm_flag=false>
<#assign need_pca = false>
<#assign need_search = false>
<#assign need_dept_user = false>
<#assign need_switch = false>
<#assign need_dept = false>
<#assign need_multi = false>
<#assign need_popup = false>
<#assign need_popup_dict = false>
<#assign need_select_tag = false>
<#assign need_select_tree = false>
<#assign need_time = false>
<#assign need_markdown = false>
<#assign need_upload = false>
<#assign need_image_upload = false>
<#assign need_editor = false>
<#assign need_checkbox = false>
<#assign need_range_number = false>
<#assign form_span = 24>
<#if tableVo.fieldRowNum==2>
  <#assign form_span = 12>
<#elseif tableVo.fieldRowNum==3>
  <#assign form_span = 8>
<#elseif tableVo.fieldRowNum==4>
  <#assign form_span = 6>
</#if>
  <#assign hasOnlyValidate = false>
<template>
  <a-spin :spinning="loading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form v-bind="formItemLayout" name="${sub.entityName}Form" ref="formRef" class="antd-modal-form">
          <a-row>
            <#list sub.colums as po>
              <#if po.isShow == 'Y' && po.fieldValidType?default("") == 'only'>
                <#assign hasOnlyValidate = true>
              </#if>
              <#if po.fieldDbName=='bpm_status'>
                <#assign bpm_flag=true>
              </#if>
              <#assign formEntityName>${sub.entityName}Form</#assign>
              <#include "/common/form/native/vue3NativeForm.ftl">
            </#list>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, toRaw } from 'vue';
  import { query${sub.entityName}ListByMainId } from '../${entityName}.api';
<#include "/common/form/native/vue3NativeImport.ftl">
<#if hasOnlyValidate == true>
  import { duplicateValidate } from '/@/utils/helper/validator'
</#if>
  import { useMessage } from '/@/hooks/web/useMessage';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';  
  import { Form } from 'ant-design-vue';
  const useForm = Form.useForm;

  export default defineComponent({
    name: '${sub.entityName}Form',
    components:{
    <#include "/common/form/native/vue3NativeComponents.ftl">
    JFormContainer,
    },
    props:{
      disabled:{
        type: Boolean,
        default: false
      }
    },
    setup(){
      const { createMessage } = useMessage();
      const isForm = true;
      const loading = ref(false);
      const formRef = ref();
      const formData = reactive<Record<string, any>>({
        id: '',
        <#include "/common/init/native/vue3NativeSubInitValue.ftl">
      });
      //表单验证
      const validatorRules = reactive({
    <#list sub.colums as po>
    <#if po.isShow == 'Y' && poHasCheck(po)>
        ${po.fieldName}: [<#include "/common/validatorRulesTemplate/native/vue3CoreNative.ftl">],
    </#if>
    </#list>
      })
      const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, {immediate: false});
      const formItemLayout = {
        labelCol: { xs: { span: 24 }, sm: { span: 5 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      };

      async function initFormData(mainId) {
        resetFields();
        let tmpData = {}
        if(mainId){
          let list = await query${sub.entityName}ListByMainId(mainId);
          if(list && list.length>0){
            let temp = list[0];
            Object.keys(formData).forEach((key) => {
              if(temp.hasOwnProperty(key)){
                tmpData[key] = temp[key]
              }
            })
          }
        }
        //赋值
        Object.assign(formData,tmpData);
      }

      async function getFormData() {
        try {
          // 触发表单验证
          await validate();
        } catch ({ errorFields }) {
          if (errorFields) {
            const firstField = errorFields[0];
            if (firstField) {
              formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
            }
          }
          return Promise.reject(errorFields);
        }
        let subFormData = toRaw(formData);
        if(Object.keys(subFormData).length>0){
          return subFormData
        }
        return false;
      }

      function setFieldsValue(values) {
        if(values){
          Object.keys(values).map(k=>{
            formData[k] = values[k];
          });
        }
      }

      /**
       * 值改变事件触发-树控件回调
       * @param key
       * @param value
       */
      function handleFormChange(key, value) {
        formData[key] = value;
      }

    <#list sub.colums as po>
    <#if po.isShow == 'Y' && po.fieldValidType?default("") == 'only'>
      async function ${po.fieldName}Duplicatevalidate(_r, value) {
        return duplicateValidate('${sub.tableName}', '${po.fieldDbName}', value, formData.id || '')
      }
    </#if>
    </#list>

      return {
        loading,
        formData,
        formItemLayout,
        initFormData,
        getFormData,
        setFieldsValue,
        handleFormChange,
        isForm,
        validateInfos,
        formRef,
      }
    }
  });
</script>
<style lang="less" scoped></style>
</#if>
</#list>
