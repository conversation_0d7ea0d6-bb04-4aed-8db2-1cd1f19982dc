<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分片上传测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .upload-container {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        
        .upload-container:hover {
            border-color: #007bff;
        }
        
        .upload-container.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .upload-btn:hover {
            background-color: #0056b3;
        }
        
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
        }
        
        .file-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .controls {
            margin: 20px 0;
            display: none;
        }
        
        .control-btn {
            margin: 0 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .pause-btn {
            background-color: #ffc107;
            color: #212529;
        }
        
        .resume-btn {
            background-color: #28a745;
            color: white;
        }
        
        .cancel-btn {
            background-color: #dc3545;
            color: white;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .info {
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>分片上传测试页面</h1>
    
    <div class="upload-container" id="uploadContainer">
        <p>点击选择文件或拖拽文件到此处</p>
        <input type="file" id="fileInput" class="file-input">
        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
            选择文件
        </button>
    </div>
    
    <div class="file-info" id="fileInfo">
        <h3>文件信息</h3>
        <p><strong>文件名:</strong> <span id="fileName"></span></p>
        <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
        <p><strong>文件类型:</strong> <span id="fileType"></span></p>
        <p><strong>分片数量:</strong> <span id="chunkCount"></span></p>
    </div>
    
    <div class="progress-container" id="progressContainer">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text" id="progressText">0%</div>
    </div>
    
    <div class="controls" id="controls">
        <button class="control-btn pause-btn" id="pauseBtn" onclick="pauseUpload()">暂停</button>
        <button class="control-btn resume-btn" id="resumeBtn" onclick="resumeUpload()" style="display:none;">恢复</button>
        <button class="control-btn cancel-btn" id="cancelBtn" onclick="cancelUpload()">取消</button>
    </div>
    
    <div class="log" id="log"></div>

    <!-- 引入SparkMD5库 -->
    <script src="https://cdn.jsdelivr.net/npm/spark-md5@3.0.2/spark-md5.min.js"></script>
    <!-- 引入分片上传工具 -->
    <script src="chunk-upload.js"></script>
    
    <script>
        let uploader = null;
        let selectedFile = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileInput');
            const uploadContainer = document.getElementById('uploadContainer');
            
            // 文件选择事件
            fileInput.addEventListener('change', handleFileSelect);
            
            // 拖拽事件
            uploadContainer.addEventListener('dragover', handleDragOver);
            uploadContainer.addEventListener('dragleave', handleDragLeave);
            uploadContainer.addEventListener('drop', handleDrop);
        });
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectFile(file);
            }
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }
        
        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }
        
        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                selectFile(files[0]);
            }
        }
        
        function selectFile(file) {
            selectedFile = file;
            showFileInfo(file);
            log(`选择文件: ${file.name} (${formatFileSize(file.size)})`, 'info');
            
            // 开始上传
            startUpload();
        }
        
        function showFileInfo(file) {
            const chunkSize = 5 * 1024 * 1024; // 5MB
            const chunkCount = Math.ceil(file.size / chunkSize);
            
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = file.type || '未知';
            document.getElementById('chunkCount').textContent = chunkCount;
            
            document.getElementById('fileInfo').style.display = 'block';
        }
        
        function startUpload() {
            if (!selectedFile) {
                return;
            }
            
            // 显示进度条和控制按钮
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('controls').style.display = 'block';
            
            // 创建上传器
            uploader = new ChunkUploader({
                chunkSize: 5 * 1024 * 1024, // 5MB
                maxRetries: 3,
                concurrency: 3,
                onProgress: updateProgress,
                onSuccess: handleSuccess,
                onError: handleError,
                onChunkSuccess: handleChunkSuccess,
                onChunkError: handleChunkError
            });
            
            log('开始上传...', 'info');
            
            // 开始上传
            uploader.upload(selectedFile, 'upload', 'local');
        }
        
        function updateProgress(progress) {
            const percentage = progress.percentage;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = 
                `${percentage}% (${progress.uploaded}/${progress.total})`;
            
            if (progress.failed > 0) {
                log(`进度更新: ${percentage}% (失败: ${progress.failed})`, 'error');
            } else {
                log(`进度更新: ${percentage}%`, 'info');
            }
        }
        
        function handleSuccess(url) {
            log(`上传成功! 文件URL: ${url}`, 'success');
            document.getElementById('progressText').textContent = '上传完成!';
            document.getElementById('controls').style.display = 'none';
        }
        
        function handleError(error) {
            log(`上传失败: ${error.message || error}`, 'error');
        }
        
        function handleChunkSuccess(chunkIndex) {
            log(`分片 ${chunkIndex} 上传成功`, 'success');
        }
        
        function handleChunkError(chunkIndex, error) {
            log(`分片 ${chunkIndex} 上传失败: ${error.message || error}`, 'error');
        }
        
        function pauseUpload() {
            if (uploader) {
                uploader.pause();
                document.getElementById('pauseBtn').style.display = 'none';
                document.getElementById('resumeBtn').style.display = 'inline-block';
                log('上传已暂停', 'info');
            }
        }
        
        function resumeUpload() {
            if (uploader) {
                uploader.resume();
                document.getElementById('pauseBtn').style.display = 'inline-block';
                document.getElementById('resumeBtn').style.display = 'none';
                log('上传已恢复', 'info');
            }
        }
        
        function cancelUpload() {
            if (uploader) {
                uploader.cancel();
                document.getElementById('progressContainer').style.display = 'none';
                document.getElementById('controls').style.display = 'none';
                log('上传已取消', 'info');
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
    </script>
</body>
</html>
