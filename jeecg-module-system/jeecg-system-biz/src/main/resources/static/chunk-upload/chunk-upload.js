/**
 * 分片上传工具类
 * <AUTHOR>
 * @date 2025-08-04
 */
class ChunkUploader {
    constructor(options = {}) {
        this.options = {
            chunkSize: 5 * 1024 * 1024, // 默认5MB分片
            maxRetries: 3, // 最大重试次数
            concurrency: 3, // 并发上传数
            baseUrl: '/sys/chunk-upload', // API基础路径
            ...options
        };
        
        this.file = null;
        this.taskId = null;
        this.chunks = [];
        this.uploadedChunks = new Set();
        this.failedChunks = new Set();
        this.isUploading = false;
        this.isPaused = false;
        
        // 事件回调
        this.onProgress = options.onProgress || (() => {});
        this.onSuccess = options.onSuccess || (() => {});
        this.onError = options.onError || (() => {});
        this.onChunkSuccess = options.onChunkSuccess || (() => {});
        this.onChunkError = options.onChunkError || (() => {});
    }

    /**
     * 计算文件MD5
     */
    async calculateMD5(file) {
        return new Promise((resolve, reject) => {
            const spark = new SparkMD5.ArrayBuffer();
            const fileReader = new FileReader();
            const chunkSize = 2097152; // 2MB
            let currentChunk = 0;
            const chunks = Math.ceil(file.size / chunkSize);

            fileReader.onload = (e) => {
                spark.append(e.target.result);
                currentChunk++;

                if (currentChunk < chunks) {
                    loadNext();
                } else {
                    resolve(spark.end());
                }
            };

            fileReader.onerror = () => {
                reject(new Error('文件读取失败'));
            };

            function loadNext() {
                const start = currentChunk * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                fileReader.readAsArrayBuffer(file.slice(start, end));
            }

            loadNext();
        });
    }

    /**
     * 分片文件
     */
    createChunks(file) {
        const chunks = [];
        const chunkCount = Math.ceil(file.size / this.options.chunkSize);
        
        for (let i = 0; i < chunkCount; i++) {
            const start = i * this.options.chunkSize;
            const end = Math.min(start + this.options.chunkSize, file.size);
            chunks.push({
                index: i,
                start: start,
                end: end,
                blob: file.slice(start, end),
                size: end - start,
                retries: 0
            });
        }
        
        return chunks;
    }

    /**
     * 秒传检查
     */
    async checkInstantUpload(fileHash, fileSize) {
        try {
            const response = await fetch(`${this.options.baseUrl}/check-instant`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `fileHash=${fileHash}&fileSize=${fileSize}`
            });
            
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('秒传检查失败:', error);
            return { success: false, message: '秒传检查失败' };
        }
    }

    /**
     * 初始化上传任务
     */
    async initUploadTask(file, bizPath = 'upload', uploadType = 'local') {
        try {
            const fileHash = await this.calculateMD5(file);
            
            // 秒传检查
            const instantResult = await this.checkInstantUpload(fileHash, file.size);
            if (instantResult.success && instantResult.result) {
                this.onSuccess(instantResult.result);
                return { success: true, instant: true, url: instantResult.result };
            }

            const formData = new FormData();
            formData.append('fileName', file.name);
            formData.append('fileSize', file.size);
            formData.append('fileHash', fileHash);
            formData.append('chunkSize', this.options.chunkSize);
            formData.append('bizPath', bizPath);
            formData.append('uploadType', uploadType);

            const response = await fetch(`${this.options.baseUrl}/init`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.taskId = result.result.id;
                this.file = file;
                this.chunks = this.createChunks(file);
                return { success: true, taskId: this.taskId };
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('初始化上传任务失败:', error);
            this.onError(error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 上传单个分片
     */
    async uploadChunk(chunk) {
        try {
            const formData = new FormData();
            formData.append('taskId', this.taskId);
            formData.append('chunkIndex', chunk.index);
            formData.append('file', chunk.blob);

            const response = await fetch(`${this.options.baseUrl}/upload-chunk`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.uploadedChunks.add(chunk.index);
                this.failedChunks.delete(chunk.index);
                this.onChunkSuccess(chunk.index);
                this.updateProgress();
                return true;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error(`分片 ${chunk.index} 上传失败:`, error);
            this.failedChunks.add(chunk.index);
            this.onChunkError(chunk.index, error);
            
            // 重试逻辑
            if (chunk.retries < this.options.maxRetries) {
                chunk.retries++;
                console.log(`分片 ${chunk.index} 开始第 ${chunk.retries} 次重试`);
                return await this.uploadChunk(chunk);
            }
            
            return false;
        }
    }

    /**
     * 并发上传分片
     */
    async uploadChunks() {
        this.isUploading = true;
        const pendingChunks = this.chunks.filter(chunk => 
            !this.uploadedChunks.has(chunk.index) && !this.failedChunks.has(chunk.index)
        );

        // 分批并发上传
        for (let i = 0; i < pendingChunks.length; i += this.options.concurrency) {
            if (this.isPaused) {
                break;
            }

            const batch = pendingChunks.slice(i, i + this.options.concurrency);
            const promises = batch.map(chunk => this.uploadChunk(chunk));
            
            await Promise.all(promises);
        }

        this.isUploading = false;
    }

    /**
     * 合并分片
     */
    async mergeChunks() {
        try {
            const response = await fetch(`${this.options.baseUrl}/merge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `taskId=${this.taskId}`
            });

            const result = await response.json();
            
            if (result.success) {
                this.onSuccess(result.result);
                return { success: true, url: result.result };
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('合并分片失败:', error);
            this.onError(error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 开始上传
     */
    async upload(file, bizPath = 'upload', uploadType = 'local') {
        try {
            // 初始化任务
            const initResult = await this.initUploadTask(file, bizPath, uploadType);
            if (!initResult.success) {
                return initResult;
            }

            // 如果是秒传，直接返回
            if (initResult.instant) {
                return initResult;
            }

            // 上传分片
            await this.uploadChunks();

            // 检查是否有失败的分片
            if (this.failedChunks.size > 0) {
                throw new Error(`有 ${this.failedChunks.size} 个分片上传失败`);
            }

            // 合并分片
            return await this.mergeChunks();

        } catch (error) {
            console.error('上传失败:', error);
            this.onError(error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 暂停上传
     */
    pause() {
        this.isPaused = true;
    }

    /**
     * 恢复上传
     */
    async resume() {
        this.isPaused = false;
        if (!this.isUploading) {
            await this.uploadChunks();
            
            // 检查是否完成
            if (this.uploadedChunks.size === this.chunks.length) {
                return await this.mergeChunks();
            }
        }
    }

    /**
     * 取消上传
     */
    async cancel() {
        this.isPaused = true;
        this.isUploading = false;
        
        if (this.taskId) {
            try {
                await fetch(`${this.options.baseUrl}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `taskId=${this.taskId}`
                });
            } catch (error) {
                console.error('取消上传失败:', error);
            }
        }
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const progress = {
            total: this.chunks.length,
            uploaded: this.uploadedChunks.size,
            failed: this.failedChunks.size,
            percentage: Math.round((this.uploadedChunks.size / this.chunks.length) * 100)
        };
        
        this.onProgress(progress);
    }

    /**
     * 获取上传状态
     */
    async getStatus() {
        if (!this.taskId) {
            return null;
        }

        try {
            const response = await fetch(`${this.options.baseUrl}/status/${this.taskId}`);
            const result = await response.json();
            return result.success ? result.result : null;
        } catch (error) {
            console.error('获取状态失败:', error);
            return null;
        }
    }
}

// 导出到全局
window.ChunkUploader = ChunkUploader;
