package org.jeecg.modules.user_front.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 代理商金币统计信息VO
 * @Author: Alex
 * @Date: 2025-08-01
 * @Version: V1.0
 */
@Data
@ApiModel(value = "GoldenBeanStatsVO", description = "代理商金币统计信息")
public class GoldenBeanStatsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总计金币")
    private Integer totalGoldenBean;
    
    @ApiModelProperty(value = "开户使用金币")
    private Integer usedGoldenBean;
    
    @ApiModelProperty(value = "剩余金币")
    private Integer remainingGoldenBean;
    
    @ApiModelProperty(value = "统计时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statsTime;

    /**
     * 创建默认统计信息（用于异常情况）
     */
    public static GoldenBeanStatsVO createDefault() {
        GoldenBeanStatsVO stats = new GoldenBeanStatsVO();
        stats.setTotalGoldenBean(0);
        stats.setUsedGoldenBean(0);
        stats.setRemainingGoldenBean(0);
        stats.setStatsTime(new Date());
        return stats;
    }
}
