package org.jeecg.modules.inz_learning_categorys.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.jeecg.modules.inz_learning_modules.entity.InzLearningModules;
import org.jeecg.modules.inz_learning_modules.service.IInzLearningModulesService;
import org.jeecg.modules.inz_learning_videos.entity.InzLearningVideos;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 视频统计工具类
 * 用于在添加、删除视频时更新分类和模块的视频统计数据
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class VideoStatisticsUtil {

    @Autowired
    private IInzLearningCategorysService categoryService;

    @Autowired
    private IInzLearningModulesService moduleService;

    /**
     * 添加视频后更新统计信息
     * @param addedVideos 新增的视频列表
     */
    public void updateStatisticsAfterAdd(List<InzLearningVideos> addedVideos) {
        updateVideoStatistics(addedVideos, 1);
    }

    /**
     * 删除视频后更新统计信息
     * @param deletedVideos 删除的视频列表
     */
    public void updateStatisticsAfterDelete(List<InzLearningVideos> deletedVideos) {
        updateVideoStatistics(deletedVideos, -1);
    }

    /**
     * 单个视频添加后更新统计
     * @param video 新增的视频
     */
    public void updateStatisticsAfterAdd(InzLearningVideos video) {
        if (video != null && video.getCategoryId() != null) {
            updateCategoryVideoCount(video.getCategoryId(), 1);
            
            // 更新模块统计
            InzLearningCategorys category = categoryService.getById(video.getCategoryId());
            if (category != null && category.getModuleId() != null) {
                updateModuleVideoCount(category.getModuleId(), 1);
            }
        }
    }

    /**
     * 单个视频删除后更新统计
     * @param video 删除的视频
     */
    public void updateStatisticsAfterDelete(InzLearningVideos video) {
        if (video != null && video.getCategoryId() != null) {
            updateCategoryVideoCount(video.getCategoryId(), -1);
            
            // 更新模块统计
            InzLearningCategorys category = categoryService.getById(video.getCategoryId());
            if (category != null && category.getModuleId() != null) {
                updateModuleVideoCount(category.getModuleId(), -1);
            }
        }
    }

    /**
     * 视频分类变更后更新统计
     * @param video 视频对象
     * @param oldCategoryId 原分类ID
     * @param newCategoryId 新分类ID
     */
    public void updateStatisticsAfterCategoryChange(InzLearningVideos video, String oldCategoryId, String newCategoryId) {
        if (oldCategoryId != null && !oldCategoryId.equals(newCategoryId)) {
            // 原分类减1
            updateCategoryVideoCount(oldCategoryId, -1);
            
            // 更新原分类的模块统计
            InzLearningCategorys oldCategory = categoryService.getById(oldCategoryId);
            if (oldCategory != null && oldCategory.getModuleId() != null) {
                updateModuleVideoCount(oldCategory.getModuleId(), -1);
            }
        }

        if (newCategoryId != null && !newCategoryId.equals(oldCategoryId)) {
            // 新分类加1
            updateCategoryVideoCount(newCategoryId, 1);
            
            // 更新新分类的模块统计
            InzLearningCategorys newCategory = categoryService.getById(newCategoryId);
            if (newCategory != null && newCategory.getModuleId() != null) {
                updateModuleVideoCount(newCategory.getModuleId(), 1);
            }
        }
    }

    /**
     * 重新计算分类的视频统计（用于数据修复）
     * @param categoryId 分类ID
     */
    public void recalculateCategoryStatistics(String categoryId) {
        try {
            QueryWrapper<InzLearningVideos> wrapper = new QueryWrapper<>();
            wrapper.eq("category_id", categoryId);
            // 这里需要注入视频Service，暂时先记录日志
            log.info("需要重新计算分类视频统计 - 分类ID: {}", categoryId);
            
            // TODO: 注入视频Service后实现实际的重新计算逻辑
            
        } catch (Exception e) {
            log.error("重新计算分类视频统计失败 - 分类ID: {}", categoryId, e);
        }
    }

    /**
     * 重新计算模块的视频统计（用于数据修复）
     * @param moduleId 模块ID
     */
    public void recalculateModuleStatistics(String moduleId) {
        try {
            QueryWrapper<InzLearningCategorys> wrapper = new QueryWrapper<>();
            wrapper.eq("module_id", moduleId);
            List<InzLearningCategorys> categories = categoryService.list(wrapper);
            
            int totalVideos = categories.stream()
                    .mapToInt(category -> category.getTotalVideos() != null ? category.getTotalVideos() : 0)
                    .sum();

            InzLearningModules module = moduleService.getById(moduleId);
            if (module != null) {
                module.setTotalVideos(totalVideos);
                module.setUpdateTime(new Date());
                moduleService.updateById(module);
                
                log.info("重新计算模块视频统计完成 - 模块ID: {}, 总视频数: {}", moduleId, totalVideos);
            }
            
        } catch (Exception e) {
            log.error("重新计算模块视频统计失败 - 模块ID: {}", moduleId, e);
        }
    }

    /**
     * 批量更新视频统计
     * @param videos 视频列表
     * @param multiplier 乘数（1表示增加，-1表示减少）
     */
    private void updateVideoStatistics(List<InzLearningVideos> videos, int multiplier) {
        try {
            if (videos == null || videos.isEmpty()) {
                return;
            }

            log.info("开始批量更新视频统计 - 视频数量: {}, 操作: {}", 
                    videos.size(), multiplier > 0 ? "增加" : "减少");

            // 按分类ID分组统计
            Map<String, Long> categoryVideoCount = videos.stream()
                    .collect(Collectors.groupingBy(InzLearningVideos::getCategoryId, Collectors.counting()));

            // 按模块ID分组统计
            Map<String, Long> moduleVideoCount = new HashMap<>();

            for (Map.Entry<String, Long> entry : categoryVideoCount.entrySet()) {
                String categoryId = entry.getKey();
                Long videoCount = entry.getValue();

                // 更新分类的视频统计
                updateCategoryVideoCount(categoryId, (int) (videoCount * multiplier));

                // 获取分类信息以更新模块统计
                InzLearningCategorys category = categoryService.getById(categoryId);
                if (category != null && category.getModuleId() != null) {
                    moduleVideoCount.merge(category.getModuleId(), videoCount, Long::sum);
                }
            }

            // 更新模块的视频统计
            for (Map.Entry<String, Long> entry : moduleVideoCount.entrySet()) {
                String moduleId = entry.getKey();
                Long videoCount = entry.getValue();
                updateModuleVideoCount(moduleId, (int) (videoCount * multiplier));
            }

            log.info("批量更新视频统计完成 - 影响分类: {}, 影响模块: {}", 
                    categoryVideoCount.size(), moduleVideoCount.size());

        } catch (Exception e) {
            log.error("批量更新视频统计失败", e);
        }
    }

    /**
     * 更新分类的视频数量统计
     * @param categoryId 分类ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateCategoryVideoCount(String categoryId, int deltaCount) {
        try {
            InzLearningCategorys category = categoryService.getById(categoryId);
            if (category != null) {
                int currentCount = category.getTotalVideos() != null ? category.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                category.setTotalVideos(newCount);
                category.setUpdateTime(new Date());
                categoryService.updateById(category);

                log.info("更新分类视频统计 - 分类ID: {}, 原数量: {}, 变化: {}, 新数量: {}", 
                        categoryId, currentCount, deltaCount, newCount);

                // 如果是子分类，还需要更新父分类的统计
                if (category.getParentId() != null && !"0".equals(category.getParentId())) {
                    updateCategoryVideoCount(category.getParentId(), deltaCount);
                }
            }
        } catch (Exception e) {
            log.error("更新分类视频统计失败 - 分类ID: {}, 变化数量: {}", categoryId, deltaCount, e);
        }
    }

    /**
     * 更新模块的视频数量统计
     * @param moduleId 模块ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateModuleVideoCount(String moduleId, int deltaCount) {
        try {
            InzLearningModules module = moduleService.getById(moduleId);
            if (module != null) {
                int currentCount = module.getTotalVideos() != null ? module.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                module.setTotalVideos(newCount);
                module.setUpdateTime(new Date());
                moduleService.updateById(module);

                log.info("更新模块视频统计 - 模块ID: {}, 原数量: {}, 变化: {}, 新数量: {}", 
                        moduleId, currentCount, deltaCount, newCount);
            }
        } catch (Exception e) {
            log.error("更新模块视频统计失败 - 模块ID: {}, 变化数量: {}", moduleId, deltaCount, e);
        }
    }
}
