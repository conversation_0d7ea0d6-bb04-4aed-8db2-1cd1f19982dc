package org.jeecg.modules.user_front.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 当前登录用户信息VO
 * @Author: Alex
 * @Date: 2025-08-02
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CurrentUserInfoVO", description = "当前登录用户信息")
public class CurrentUserInfoVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "用户名")
    private String username;
    
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    
    @ApiModelProperty(value = "手机号")
    private String phone;
    
    @ApiModelProperty(value = "角色代码")
    private String roleCode;
    
    @ApiModelProperty(value = "角色描述")
    private String roleDescription;
    
    @ApiModelProperty(value = "是否为代理商")
    private Boolean isAgent;
    
    @ApiModelProperty(value = "是否为系统管理员")
    private Boolean isAdmin;
    
    @ApiModelProperty(value = "权限列表")
    private List<String> permissions;

    /**
     * 创建默认用户信息（用于异常情况）
     */
    public static CurrentUserInfoVO createDefault(String username) {
        CurrentUserInfoVO userInfo = new CurrentUserInfoVO();
        userInfo.setUsername(username);
        userInfo.setRoleCode("");
        userInfo.setRoleDescription("普通用户");
        userInfo.setIsAgent(false);
        userInfo.setIsAdmin(false);
        return userInfo;
    }
}
