package org.jeecg.modules.inz_words_articles.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 短文表
 * @Author: jeecg-boot
 * @Date: 2025-07-06
 * @Version: V1.0
 */
@Data
@TableName("inz_word_article")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "inz_word_article对象", description = "短文表")
public class InzWordsArticles implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private java.lang.String id;
    /**
     * 词书ID
     */
    @Excel(name = "词书ID", width = 15)
    @ApiModelProperty(value = "词书ID")
    private java.lang.String bookId;
    /**
     * 章节ID
     */
    @Excel(name = "章节ID", width = 15)
    @ApiModelProperty(value = "章节ID")
    private java.lang.String chapterId;
    /**
     * 单词ID列表，以逗号分隔
     */
    @Excel(name = "单词ID列表，以逗号分隔", width = 15)
    @ApiModelProperty(value = "单词ID列表，以逗号分隔")
    private java.lang.String wordIds;
    /**
     * 单词列表，以逗号分隔
     */
    @Excel(name = "单词列表，以逗号分隔", width = 15)
    @ApiModelProperty(value = "单词列表，以逗号分隔")
    private java.lang.String words;
    /**
     * 短文内容
     */
    @Excel(name = "短文内容", width = 15)
    @ApiModelProperty(value = "短文内容")
    private java.lang.String content;
    /**
     * 中文释义
     */
    @Excel(name = "中文释义", width = 15)
    @ApiModelProperty(value = "中文释义")
    private java.lang.String chineseTranslation;
    /**
     * 短文标题
     */
    @Excel(name = "短文标题", width = 15)
    @ApiModelProperty(value = "短文标题")
    private java.lang.String title;
    /**
     * 组序号（同一章节内的组序号）
     */
    @Excel(name = "组序号（同一章节内的组序号）", width = 15)
    @ApiModelProperty(value = "组序号（同一章节内的组序号）")
    private java.lang.Integer groupIndex;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    @Excel(name = "问题列表JSON", width = 15)
    @ApiModelProperty(value = "问题列表JSON")
    private String questionsJson;

    @Excel(name = "question", width = 15)
    @ApiModelProperty(value = "question")
    private String question;

    @Excel(name = "options", width = 15)
    @ApiModelProperty(value = "options")
    private Map<String, Objects> options;

    @Excel(name = "answer", width = 15)
    @ApiModelProperty(value = "answer")
    private String answer;

    @Excel(name = "questionChMeaning", width = 15)
    @ApiModelProperty(value = "questionChMeaning")
    private String questionChMeaning;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
}
