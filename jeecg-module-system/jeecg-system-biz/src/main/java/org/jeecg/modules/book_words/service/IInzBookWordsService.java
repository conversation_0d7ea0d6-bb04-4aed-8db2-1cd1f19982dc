package org.jeecg.modules.book_words.service;

import org.jeecg.modules.book_words.entity.InzBookWords;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 词书单词
 * @Author: jeecg-boot
 * @Date:   2023-05-21
 * @Version: V1.0
 */
public interface IInzBookWordsService extends IService<InzBookWords> {

    /**
     * 获取指定词书章节中的所有单词
     * 
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @return 单词列表
     */
    List<InzBookWords> getWordsByChapter(String bookId, String chapterId);

    /**
     * 为指定词书章节的单词生成短文
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     */
    void generateArticlesForChapter(String bookId, String chapterId,String fileName);

    void deleteByWordId(String id);
}
