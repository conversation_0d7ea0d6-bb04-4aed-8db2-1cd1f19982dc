package org.jeecg.modules.inz_learning_videos.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.jeecg.modules.inz_learning_modules.entity.InzLearningModules;
import org.jeecg.modules.inz_learning_modules.service.IInzLearningModulesService;
import org.jeecg.modules.inz_learning_videos.dto.VideoAddByNameDTO;
import org.jeecg.modules.inz_learning_videos.entity.InzLearningVideos;
import org.jeecg.modules.inz_learning_videos.service.IInzLearningVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 继续深造-视频
 * @Author: jeecg-boot
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Api(tags = "继续深造-视频")
@RestController
@RequestMapping("/inz_learning_videos/inzLearningVideos")
@Slf4j
public class InzLearningVideosController extends JeecgController<InzLearningVideos, IInzLearningVideosService> {
    @Autowired
    private IInzLearningVideosService inzLearningVideosService;

    @Autowired
    private IInzLearningCategorysService inzLearningCategorysService;

    @Autowired
    private IInzLearningModulesService inzLearningModulesService;

    /**
     * 分页列表查询
     *
     * @param inzLearningVideos
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "继续深造-视频-分页列表查询")
    @ApiOperation(value = "继续深造-视频-分页列表查询", notes = "继续深造-视频-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzLearningVideos>> queryPageList(InzLearningVideos inzLearningVideos,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          @RequestParam(name = "moduleName", required = false) String moduleName,
                                                          @RequestParam(name = "categoryName", required = false) String categoryName,
                                                          HttpServletRequest req) {
        QueryWrapper<InzLearningVideos> queryWrapper = QueryGenerator.initQueryWrapper(inzLearningVideos, req.getParameterMap());
        queryWrapper.orderByAsc("sort_order");
        if (moduleName != null && !moduleName.isEmpty()) {
            queryWrapper.eq("module_name", moduleName);
        }
        if (categoryName != null && !categoryName.isEmpty()) {
            queryWrapper.eq("category_name", categoryName);
        }
        Page<InzLearningVideos> page = new Page<InzLearningVideos>(pageNo, pageSize);
        IPage<InzLearningVideos> pageList = inzLearningVideosService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加视频
     *
     * @param videoAddDTO 视频添加参数
     * @return
     */
    @AutoLog(value = "继续深造-视频-添加")
    @ApiOperation(value = "继续深造-视频-添加", notes = "添加视频")
    @RequiresPermissions("inz_learning_videos:inz_learning_video:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody VideoAddByNameDTO videoAddDTO) {
        try {

            // 1. 验证必填字段
            if (videoAddDTO.getModuleId() == null || videoAddDTO.getModuleId().trim().isEmpty()) {
                return Result.error("模块ID不能为空");
            }

            if (videoAddDTO.getCategoryId() == null || videoAddDTO.getCategoryId().trim().isEmpty()) {
                return Result.error("分类ID不能为空");
            }

            if (videoAddDTO.getVideoTitle() == null || videoAddDTO.getVideoTitle().trim().isEmpty()) {
                return Result.error("视频标题不能为空");
            }

            // 2. 验证模块是否存在
            InzLearningModules module = inzLearningModulesService.getById(videoAddDTO.getModuleId());
            if (module == null) {
                return Result.error("模块不存在，请检查模块ID");
            }

            // 3. 验证分类是否存在
            InzLearningCategorys category = inzLearningCategorysService.getById(videoAddDTO.getCategoryId());
            if (category == null) {
                return Result.error("分类不存在，请检查分类ID");
            }

            // 4. 验证分类是否属于指定模块
            if (!category.getModuleId().equals(videoAddDTO.getModuleId())) {
                return Result.error("分类不属于指定的模块");
            }

            // 5. 验证是否为二级分类
            if (category.getLevel() == null || category.getLevel() <= 1) {
                return Result.error("只能在二级分类下添加视频");
            }

            // 6. 检查同分类下是否已存在同名视频
            QueryWrapper<InzLearningVideos> videoCheckWrapper = new QueryWrapper<>();
            videoCheckWrapper.eq("category_id", videoAddDTO.getCategoryId())
                    .eq("video_title", videoAddDTO.getVideoTitle().trim());
            long existCount = inzLearningVideosService.count(videoCheckWrapper);

            if (existCount > 0) {
                return Result.error("该分类下已存在同名的视频：" + videoAddDTO.getVideoTitle());
            }

            // 7. 获取排序号
            Integer sortOrder = videoAddDTO.getSortOrder();
            if (sortOrder == null) {
                sortOrder = getNextSortOrder(videoAddDTO.getCategoryId());
            }

            // 8. 创建视频记录
            InzLearningVideos video = new InzLearningVideos();
            video.setModuleId(videoAddDTO.getModuleId());
            video.setCategoryId(videoAddDTO.getCategoryId());
            video.setVideoTitle(videoAddDTO.getVideoTitle().trim());
            video.setDescription(videoAddDTO.getDescription());
            video.setVideoUrl(videoAddDTO.getVideoUrl());
            video.setCoverImage(videoAddDTO.getCoverImage());
            video.setSortOrder(sortOrder);
            video.setStatus(videoAddDTO.getStatus() != null ? videoAddDTO.getStatus() : 1); // 默认启用
            video.setCreateTime(new Date());
            video.setUpdateTime(new Date());

            // 9. 保存视频
            boolean saveResult = inzLearningVideosService.save(video);
            if (!saveResult) {
                return Result.error("保存视频失败，请重试");
            }

            // 10. 更新分类的视频数量
            updateCategoryVideoCount(videoAddDTO.getCategoryId());

            return Result.OK("添加成功！视频ID: " + video.getId());

        } catch (Exception e) {
            return Result.error("添加视频失败: " + e.getMessage());
        }
    }


    /**
     * 编辑
     *
     * @param inzLearningVideos
     * @return
     */
    @AutoLog(value = "继续深造-视频-编辑")
    @ApiOperation(value = "继续深造-视频-编辑", notes = "继续深造-视频-编辑")
    @RequiresPermissions("inz_learning_videos:inz_learning_video:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningVideos inzLearningVideos) {
        inzLearningVideosService.updateById(inzLearningVideos);
        return Result.OK("编辑成功!");
    }

    /**
     * 根据分类ID查询视频列表
     *
     * @param categoryId 分类ID
     * @param pageNo     页码
     * @param pageSize   页大小
     * @return 视频列表
     */
    @ApiOperation(value = "根据分类查询视频列表", notes = "根据分类ID查询该分类下的所有视频")
    @GetMapping(value = "/list-by-category/{categoryId}")
    public Result<IPage<InzLearningVideos>> getVideosByCategory(
            @PathVariable("categoryId") String categoryId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            log.info("查询分类视频列表 - 分类ID: {}, 页码: {}, 页大小: {}", categoryId, pageNo, pageSize);

            QueryWrapper<InzLearningVideos> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId)
                    .eq("status", 1) // 只查询启用状态的视频
                    .orderByAsc("sort_order", "create_time");

            Page<InzLearningVideos> page = new Page<>(pageNo, pageSize);
            IPage<InzLearningVideos> pageList = inzLearningVideosService.page(page, queryWrapper);

            log.info("查询结果 - 分类ID: {}, 总数: {}, 当前页数据: {}",
                    categoryId, pageList.getTotal(), pageList.getRecords().size());

            return Result.OK(pageList);

        } catch (Exception e) {
            log.error("查询分类视频列表失败 - 分类ID: {}", categoryId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "继续深造-视频-通过id删除")
    @ApiOperation(value = "继续深造-视频-通过id删除", notes = "继续深造-视频-通过id删除")
    @RequiresPermissions("inz_learning_videos:inz_learning_video:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzLearningVideosService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "继续深造-视频-批量删除")
    @ApiOperation(value = "继续深造-视频-批量删除", notes = "继续深造-视频-批量删除")
    @RequiresPermissions("inz_learning_videos:inz_learning_video:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzLearningVideosService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "继续深造-视频-通过id查询")
    @ApiOperation(value = "继续深造-视频-通过id查询", notes = "继续深造-视频-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzLearningVideos> queryById(@RequestParam(name = "id", required = true) String id) {
        InzLearningVideos inzLearningVideos = inzLearningVideosService.getById(id);
        if (inzLearningVideos == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzLearningVideos);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzLearningVideos
     */
    @RequiresPermissions("inz_learning_videos:inz_learning_video:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzLearningVideos inzLearningVideos) {
        return super.exportXls(request, inzLearningVideos, InzLearningVideos.class, "继续深造-视频");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_learning_videos:inz_learning_video:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzLearningVideos.class);
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSortOrder(String categoryId) {
        try {
            QueryWrapper<InzLearningVideos> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId)
                    .orderByDesc("sort_order")
                    .last("LIMIT 1");
            InzLearningVideos lastVideo = inzLearningVideosService.getOne(queryWrapper);
            return (lastVideo != null && lastVideo.getSortOrder() != null)
                    ? lastVideo.getSortOrder() + 1 : 1;
        } catch (Exception e) {
            log.error("获取排序号失败 - 分类ID: {}", categoryId, e);
            return 1;
        }
    }

    /**
     * 更新分类的视频数量
     */
    private void updateCategoryVideoCount(String categoryId) {
        try {
            QueryWrapper<InzLearningVideos> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId).eq("status", 1);
            long videoCount = inzLearningVideosService.count(queryWrapper);

            InzLearningCategorys category = inzLearningCategorysService.getById(categoryId);
            if (category != null) {
                category.setTotalVideos((int) videoCount);
                category.setUpdateTime(new Date());
                inzLearningCategorysService.updateById(category);
                log.info("更新分类视频数量 - 分类ID: {}, 视频数量: {}", categoryId, videoCount);
            }
        } catch (Exception e) {
            log.error("更新分类视频数量失败 - 分类ID: {}", categoryId, e);
        }
    }

}
