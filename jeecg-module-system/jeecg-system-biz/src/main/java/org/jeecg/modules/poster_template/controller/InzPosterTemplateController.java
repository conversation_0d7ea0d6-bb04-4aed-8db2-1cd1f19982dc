package org.jeecg.modules.poster_template.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.poster_template.entity.InzPosterTemplate;
import org.jeecg.modules.poster_template.service.IInzPosterTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

 /**
 * @Description: 海报表
 * @Author: jeecg-boot
 * @Date:   2025-06-11
 * @Version: V1.0
 */
@Api(tags="海报表")
@RestController
@RequestMapping("/poster_template/inzPosterTemplate")
@Slf4j
public class InzPosterTemplateController extends JeecgController<InzPosterTemplate, IInzPosterTemplateService> {
	@Autowired
	private IInzPosterTemplateService inzPosterTemplateService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzPosterTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "海报表-分页列表查询")
	@ApiOperation(value="海报表-分页列表查询", notes="海报表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzPosterTemplate>> queryPageList(InzPosterTemplate inzPosterTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzPosterTemplate> queryWrapper = QueryGenerator.initQueryWrapper(inzPosterTemplate, req.getParameterMap());
		Page<InzPosterTemplate> page = new Page<InzPosterTemplate>(pageNo, pageSize);
		IPage<InzPosterTemplate> pageList = inzPosterTemplateService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzPosterTemplate
	 * @return
	 */
	@AutoLog(value = "海报表-添加")
	@ApiOperation(value="海报表-添加", notes="海报表-添加")
	@RequiresPermissions("poster_template:inz_poster_template:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzPosterTemplate inzPosterTemplate) throws IOException {
		// 从相对路径获取图片宽高
		String relativePath = inzPosterTemplate.getBackgroundImage(); // 例如 "temp/企业微信截图_XXX.png"
		int[] dimensions = CommonUtils.getImageSizeFromRelativePath(uploadPath + "/" + relativePath);

		inzPosterTemplate.setBackgroundImageWidth(dimensions[0]);
		inzPosterTemplate.setBackgroundImageHeight(dimensions[1]);
		inzPosterTemplateService.save(inzPosterTemplate);
		return Result.OK("添加成功！");
	}
	 @Value("${jeecg.path.upload}")
	 private String uploadPath;
	/**
	 *  编辑
	 *
	 * @param inzPosterTemplate
	 * @return
	 */
	@AutoLog(value = "海报表-编辑")
	@ApiOperation(value="海报表-编辑", notes="海报表-编辑")
	@RequiresPermissions("poster_template:inz_poster_template:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzPosterTemplate inzPosterTemplate) throws IOException {
		// 从相对路径获取图片宽高
		String relativePath = inzPosterTemplate.getBackgroundImage(); // 例如 "temp/企业微信截图_XXX.png"
		int[] dimensions = CommonUtils.getImageSizeFromRelativePath(uploadPath + "/" + relativePath);

		inzPosterTemplate.setBackgroundImageWidth(dimensions[0]);
		inzPosterTemplate.setBackgroundImageHeight(dimensions[1]);
		inzPosterTemplateService.updateById(inzPosterTemplate);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "海报表-通过id删除")
	@ApiOperation(value="海报表-通过id删除", notes="海报表-通过id删除")
	@RequiresPermissions("poster_template:inz_poster_template:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzPosterTemplateService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "海报表-批量删除")
	@ApiOperation(value="海报表-批量删除", notes="海报表-批量删除")
	@RequiresPermissions("poster_template:inz_poster_template:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzPosterTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "海报表-通过id查询")
	@ApiOperation(value="海报表-通过id查询", notes="海报表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzPosterTemplate> queryById(@RequestParam(name="id",required=true) String id) {
		InzPosterTemplate inzPosterTemplate = inzPosterTemplateService.getById(id);
		if(inzPosterTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzPosterTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzPosterTemplate
    */
    @RequiresPermissions("poster_template:inz_poster_template:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzPosterTemplate inzPosterTemplate) {
        return super.exportXls(request, inzPosterTemplate, InzPosterTemplate.class, "海报表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("poster_template:inz_poster_template:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzPosterTemplate.class);
    }

}
