import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '类别',
    align:"center",
    dataIndex: 'category'
   },
   {
    title: '音频文件路径',
    align:"center",
    dataIndex: 'audioUrl',
   },
   {
    title: '视频文件路径',
    align:"center",
    dataIndex: 'videoUrl',
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入名称!'},
          ];
     },
  },
  {
    label: '类别',
    field: 'category',
    component: 'Input',
  },
  {
    label: '音频文件路径',
    field: 'audioUrl',
    component: 'JUpload',
    componentProps:{
     },
  },
  {
    label: '视频文件路径',
    field: 'videoUrl',
    component: 'JUpload',
    componentProps:{
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '名称',order: 0,view: 'text', type: 'string',},
  category: {title: '类别',order: 1,view: 'text', type: 'string',},
  audioUrl: {title: '音频文件路径',order: 2,view: 'file', type: 'string',},
  videoUrl: {title: '视频文件路径',order: 3,view: 'file', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}