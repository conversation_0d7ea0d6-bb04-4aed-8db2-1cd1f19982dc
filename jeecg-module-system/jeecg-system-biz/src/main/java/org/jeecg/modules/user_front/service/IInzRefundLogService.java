package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_front.entity.InzRefundLog;
import org.jeecg.modules.user_front.controller.RefundController.RefundHistoryVO;

import java.util.Date;
import java.util.List;

/**
 * @Description: 退单记录Service接口
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzRefundLogService extends IService<InzRefundLog> {

    /**
     * 创建退单记录
     * @param userId 用户ID
     * @param operatorId 操作者ID
     * @param permissionRecordId 权限记录ID
     * @param refundReason 退单原因
     * @param refundAmount 退款金币数量
     * @param originalAmount 原始金币数量
     * @param isSecondRefund 是否为二次退单
     * @param permissionStartTime 权限开始时间
     * @return 是否创建成功
     */
    boolean createRefundRecord(String userId, String operatorId, String permissionRecordId,
                              String refundReason, Integer refundAmount, Integer originalAmount,
                              Boolean isSecondRefund, Date permissionStartTime);

    /**
     * 查询用户退单历史
     * @param userId 用户ID
     * @return 退单历史记录列表
     */
    List<RefundHistoryVO> getRefundHistory(String userId);

    /**
     * 检查用户是否有退单历史
     * @param userId 用户ID
     * @return 是否有退单历史
     */
    boolean hasRefundHistory(String userId);

    /**
     * 获取用户退单次数
     * @param userId 用户ID
     * @return 退单次数
     */
    Integer getRefundCount(String userId);

    /**
     * 获取用户最新的退单记录
     * @param userId 用户ID
     * @return 最新退单记录
     */
    InzRefundLog getLatestRefund(String userId);
}
