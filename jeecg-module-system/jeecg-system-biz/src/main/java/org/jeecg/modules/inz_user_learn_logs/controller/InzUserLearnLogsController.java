package org.jeecg.modules.inz_user_learn_logs.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.education.entity.InzEducation;
import org.jeecg.modules.inz_user_learn_logs.entity.InzUserLearnLogs;
import org.jeecg.modules.inz_user_learn_logs.service.IInzUserLearnLogsService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 学习记录
 * @Author: jeecg-boot
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@Api(tags = "学习记录")
@RestController
@RequestMapping("/inz_user_learn_logs/inzUserLearnLogs")
@Slf4j
public class InzUserLearnLogsController extends JeecgController<InzUserLearnLogs, IInzUserLearnLogsService> {
    @Autowired
    private IInzUserLearnLogsService inzUserLearnLogsService;
    @Autowired
    private IInzUserFrontService inzUserFrontService;

    /**
     * 分页列表查询 - 支持权限控制
     *
     * @param inzUserLearnLogs
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @PermissionData(pageComponent = "inz_user_learn_logs/InzUserLearnLogsList")
    @ApiOperation(value = "学习记录-分页列表查询", notes = "学习记录-分页列表查询，根据权限展示不同用户数据")
    @GetMapping(value = "/list")
    public Result<IPage<InzUserLearnLogs>> queryPageList(InzUserLearnLogs inzUserLearnLogs,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        QueryWrapper<InzUserLearnLogs> queryWrapper = QueryGenerator.initQueryWrapper(inzUserLearnLogs, req.getParameterMap());

        // 调用Service层的权限控制方法
        return inzUserLearnLogsService.queryPageList(req, queryWrapper, pageSize, pageNo);
    }

    /**
     * 获取所有学习记录数据（查询全部记录，不做权限隔离）- 仅超级管理员可用
     *
     * @param inzUserLearnLogs
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:listAll")
    @ApiOperation(value = "学习记录-查询全部", notes = "学习记录-查询全部，不做权限隔离")
    @GetMapping(value = "/listAll")
    public Result<IPage<InzUserLearnLogs>> queryAllPageList(InzUserLearnLogs inzUserLearnLogs,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        QueryWrapper<InzUserLearnLogs> queryWrapper = QueryGenerator.initQueryWrapper(inzUserLearnLogs, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");

        Page<InzUserLearnLogs> page = new Page<InzUserLearnLogs>(pageNo, pageSize);
        IPage<InzUserLearnLogs> pageList = inzUserLearnLogsService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    /**
     * 根据用户ID查询学习记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 页面大小
     * @param req 请求对象
     * @return 学习记录列表
     */
    @ApiOperation(value = "根据用户ID查询学习记录", notes = "根据用户ID查询学习记录")
    @GetMapping(value = "/listByUserId")
    public Result<IPage<InzUserLearnLogs>> queryByUserId(@RequestParam(name = "userId", required = true) String userId,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        // 设置userId参数
        req.setAttribute("userId", userId);

        QueryWrapper<InzUserLearnLogs> queryWrapper = new QueryWrapper<>();
        return inzUserLearnLogsService.queryPageList(req, queryWrapper, pageSize, pageNo);
    }

    /**
     * 根据手机号查询学习记录
     *
     * @param phone 手机号
     * @param pageNo 页码
     * @param pageSize 页面大小
     * @param req 请求对象
     * @return 学习记录列表
     */
    @ApiOperation(value = "根据手机号查询学习记录", notes = "根据手机号查询学习记录")
    @GetMapping(value = "/listByPhone")
    public Result<IPage<InzUserLearnLogs>> queryByPhone(@RequestParam(name = "phone", required = true) String phone,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        // 设置phone参数
        req.setAttribute("phone", phone);

        QueryWrapper<InzUserLearnLogs> queryWrapper = new QueryWrapper<>();
        return inzUserLearnLogsService.queryPageList(req, queryWrapper, pageSize, pageNo);
    }

    /**
     * 根据多个用户ID查询学习记录
     *
     * @param userIds 用户ID列表，逗号分隔
     * @param pageNo 页码
     * @param pageSize 页面大小
     * @param req 请求对象
     * @return 学习记录列表
     */
    @ApiOperation(value = "根据多个用户ID查询学习记录", notes = "根据多个用户ID查询学习记录")
    @GetMapping(value = "/listByUserIds")
    public Result<IPage<InzUserLearnLogs>> queryByUserIds(@RequestParam(name = "userIds", required = true) String userIds,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        // 设置userIds参数
        req.setAttribute("userIds", userIds);

        QueryWrapper<InzUserLearnLogs> queryWrapper = new QueryWrapper<>();
        return inzUserLearnLogsService.queryPageList(req, queryWrapper, pageSize, pageNo);
    }

    /**
     * 获取当前用户的学习记录
     *
     * @param pageNo 页码
     * @param pageSize 页面大小
     * @param req 请求对象
     * @return 当前用户的学习记录列表
     */
    @ApiOperation(value = "获取当前用户学习记录", notes = "获取当前登录用户的学习记录")
    @GetMapping(value = "/myLearnLogs")
    public Result<IPage<InzUserLearnLogs>> queryMyLearnLogs(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        QueryWrapper<InzUserLearnLogs> queryWrapper = new QueryWrapper<>();
        // 这个接口会自动根据当前登录用户过滤数据
        return inzUserLearnLogsService.queryPageList(req, queryWrapper, pageSize, pageNo);
    }

    /**
     * 添加
     *
     * @param inzUserLearnLogs
     * @return
     */
    @AutoLog(value = "学习记录-添加")
    @ApiOperation(value = "学习记录-添加", notes = "学习记录-添加")
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzUserLearnLogs inzUserLearnLogs) {
        inzUserLearnLogsService.save(inzUserLearnLogs);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzUserLearnLogs
     * @return
     */
    @AutoLog(value = "学习记录-编辑")
    @ApiOperation(value = "学习记录-编辑", notes = "学习记录-编辑")
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzUserLearnLogs inzUserLearnLogs) {
        inzUserLearnLogsService.updateById(inzUserLearnLogs);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学习记录-通过id删除")
    @ApiOperation(value = "学习记录-通过id删除", notes = "学习记录-通过id删除")
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzUserLearnLogsService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学习记录-批量删除")
    @ApiOperation(value = "学习记录-批量删除", notes = "学习记录-批量删除")
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzUserLearnLogsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "学习记录-通过id查询")
    @ApiOperation(value = "学习记录-通过id查询", notes = "学习记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzUserLearnLogs> queryById(@RequestParam(name = "id", required = true) String id) {
        InzUserLearnLogs inzUserLearnLogs = inzUserLearnLogsService.getById(id);
        if (inzUserLearnLogs == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzUserLearnLogs);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzUserLearnLogs
     */
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzUserLearnLogs inzUserLearnLogs) {
        return super.exportXls(request, inzUserLearnLogs, InzUserLearnLogs.class, "学习记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_user_learn_logs:inz_user_learn_log:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzUserLearnLogs.class);
    }

}
