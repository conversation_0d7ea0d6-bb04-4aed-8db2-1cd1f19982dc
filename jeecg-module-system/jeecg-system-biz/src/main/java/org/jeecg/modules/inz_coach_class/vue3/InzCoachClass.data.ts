import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '班级名称',
    align:"center",
    dataIndex: 'className'
   },
   {
    title: '班级编码',
    align:"center",
    dataIndex: 'classCode'
   },
   {
    title: '教练ID（创建者）',
    align:"center",
    dataIndex: 'coachId'
   },
   {
    title: '教练姓名',
    align:"center",
    dataIndex: 'coachName'
   },
   {
    title: '班级描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '当前学生数量',
    align:"center",
    dataIndex: 'currentStudents'
   },
   {
    title: '状态 0-停用 1-启用',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '班级名称',
    field: 'className',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入班级名称!'},
          ];
     },
  },
  {
    label: '班级编码',
    field: 'classCode',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入班级编码!'},
          ];
     },
  },
  {
    label: '教练ID（创建者）',
    field: 'coachId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入教练ID（创建者）!'},
          ];
     },
  },
  {
    label: '教练姓名',
    field: 'coachName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入教练姓名!'},
          ];
     },
  },
  {
    label: '班级描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '当前学生数量',
    field: 'currentStudents',
    component: 'InputNumber',
  },
  {
    label: '状态 0-停用 1-启用',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  className: {title: '班级名称',order: 0,view: 'text', type: 'string',},
  classCode: {title: '班级编码',order: 1,view: 'text', type: 'string',},
  coachId: {title: '教练ID（创建者）',order: 2,view: 'text', type: 'string',},
  coachName: {title: '教练姓名',order: 3,view: 'text', type: 'string',},
  description: {title: '班级描述',order: 4,view: 'textarea', type: 'string',},
  currentStudents: {title: '当前学生数量',order: 5,view: 'number', type: 'number',},
  status: {title: '状态 0-停用 1-启用',order: 6,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}