package org.jeecg.modules.inz_learning_videos.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 视频上传响应VO
 * @Author: Alex (工程师)
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "VideoUploadVO", description = "视频上传响应对象")
public class VideoUploadVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "视频ID")
    private String videoId;

    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    @ApiModelProperty(value = "所属分类ID")
    private String categoryId;

    @ApiModelProperty(value = "所属分类名称")
    private String categoryName;

    @ApiModelProperty(value = "所属模块ID")
    private String moduleId;

    @ApiModelProperty(value = "所属模块名称")
    private String moduleName;

    @ApiModelProperty(value = "视频文件URL列表")
    private List<String> videoUrls;

    @ApiModelProperty(value = "封面图片URL")
    private String coverImageUrl;

    @ApiModelProperty(value = "视频描述")
    private String description;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "文件大小（字节）")
    private Long totalFileSize;

    @ApiModelProperty(value = "上传成功的文件数量")
    private Integer uploadedFileCount;
}
