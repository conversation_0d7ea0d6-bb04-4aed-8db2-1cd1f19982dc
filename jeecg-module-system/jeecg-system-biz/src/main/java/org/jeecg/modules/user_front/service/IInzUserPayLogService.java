package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_front.vo.InzUserPayLogVO;
import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface IInzUserPayLogService extends IService<InzUserPayLog> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<InzUserPayLog>
	 */
	public List<InzUserPayLog> selectByMainId(String mainId);

	/**
	 * 分页查询增强的金豆记录（包含用户信息）
	 *
	 * @param page 分页参数
	 * @param userId 用户ID（可选）
	 * @param phone 手机号（可选）
	 * @param realName 姓名（可选）
	 * @return IPage<InzUserPayLogVO>
	 */
	public IPage<InzUserPayLogVO> selectEnhancedPayLogPage(Page<InzUserPayLogVO> page,
	                                                       String userId,
	                                                       String phone,
	                                                       String realName);

	/**
	 * 查询指定用户的增强金豆记录
	 *
	 * @param userId 用户ID
	 * @param limit 限制条数
	 * @return List<InzUserPayLogVO>
	 */
	public List<InzUserPayLogVO> selectEnhancedPayLogByUserId(String userId, Integer limit);
}
