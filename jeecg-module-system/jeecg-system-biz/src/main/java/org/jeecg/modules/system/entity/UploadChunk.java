package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 分片上传详情实体
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@TableName("upload_chunk")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="UploadChunk对象", description="分片上传详情")
public class UploadChunk implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**分片ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "分片ID")
    private String id;

    /**任务ID*/
    @Excel(name = "任务ID", width = 15)
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**分片索引(从0开始)*/
    @Excel(name = "分片索引", width = 15)
    @ApiModelProperty(value = "分片索引(从0开始)")
    private Integer chunkIndex;

    /**分片实际大小(字节)*/
    @Excel(name = "分片大小", width = 15)
    @ApiModelProperty(value = "分片实际大小(字节)")
    private Integer chunkSize;

    /**分片MD5哈希值*/
    @ApiModelProperty(value = "分片MD5哈希值")
    private String chunkHash;

    /**存储路径*/
    @ApiModelProperty(value = "存储路径")
    private String storagePath;

    /**状态:PENDING,UPLOADING,COMPLETED,FAILED*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;

    /**重试次数*/
    @Excel(name = "重试次数", width = 15)
    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    /**错误信息*/
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    // 状态常量
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_UPLOADING = "UPLOADING";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_FAILED = "FAILED";

    // 最大重试次数
    public static final int MAX_RETRY_COUNT = 3;

    /**
     * 检查是否待上传
     * @return true if pending
     */
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }

    /**
     * 检查是否正在上传
     * @return true if uploading
     */
    public boolean isUploading() {
        return STATUS_UPLOADING.equals(status);
    }

    /**
     * 检查是否上传完成
     * @return true if completed
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }

    /**
     * 检查是否上传失败
     * @return true if failed
     */
    public boolean isFailed() {
        return STATUS_FAILED.equals(status);
    }

    /**
     * 检查是否可以重试
     * @return true if can retry
     */
    public boolean canRetry() {
        return isFailed() && (retryCount == null || retryCount < MAX_RETRY_COUNT);
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 1;
        } else {
            retryCount++;
        }
    }

    /**
     * 格式化分片大小显示
     * @return 格式化后的分片大小字符串
     */
    public String getFormattedChunkSize() {
        if (chunkSize == null) {
            return "0 B";
        }
        
        long size = chunkSize;
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", (double) size, units[unitIndex]);
    }

    /**
     * 生成分片文件名
     * @return 分片文件名
     */
    public String generateChunkFileName() {
        return String.format("chunk_%s_%d", taskId, chunkIndex);
    }
}
