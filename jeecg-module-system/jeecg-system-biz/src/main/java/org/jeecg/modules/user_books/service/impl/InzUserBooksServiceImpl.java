package org.jeecg.modules.user_books.service.impl;

import org.jeecg.modules.user_books.entity.InzUserBooks;
import org.jeecg.modules.user_books.mapper.InzUserBooksMapper;
import org.jeecg.modules.user_books.service.IInzUserBooksService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户选择图书管理
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
@Service
public class InzUserBooksServiceImpl extends ServiceImpl<InzUserBooksMapper, InzUserBooks> implements IInzUserBooksService {

}
