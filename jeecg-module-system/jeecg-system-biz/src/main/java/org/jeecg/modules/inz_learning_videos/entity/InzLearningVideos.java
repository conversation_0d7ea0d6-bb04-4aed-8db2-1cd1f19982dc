package org.jeecg.modules.inz_learning_videos.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 继续深造-视频
 * @Author: jeecg-boot
 * @Date:   2025-08-03
 * @Version: V1.0
 */
@Data
@TableName("inz_learning_video")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_learning_video对象", description="继续深造-视频")
public class InzLearningVideos implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private java.lang.String id;
	/**所属模块ID*/
	@Excel(name = "所属模块ID", width = 15)
    @ApiModelProperty(value = "所属模块ID")
    private java.lang.String moduleId;
	/**所属分类ID*/
	@Excel(name = "所属分类ID", width = 15)
    @ApiModelProperty(value = "所属分类ID")
    private java.lang.String categoryId;
	/**视频标题*/
	@Excel(name = "视频标题", width = 15)
    @ApiModelProperty(value = "视频标题")
    private java.lang.String videoTitle;
	/**视频描述*/
	@Excel(name = "视频描述", width = 15)
    @ApiModelProperty(value = "视频描述")
    private java.lang.String description;
	/**视频封面URL*/
	@Excel(name = "视频封面URL", width = 15)
    @ApiModelProperty(value = "视频封面URL")
    private java.lang.String coverImage;
	/**视频文件URL*/
	@Excel(name = "视频文件URL", width = 15)
    @ApiModelProperty(value = "视频文件URL")
    private java.lang.String videoUrl;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer sortOrder;
	/**状态 0-禁用 1-启用*/
	@Excel(name = "状态 0-禁用 1-启用", width = 15)
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private java.lang.Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
}
