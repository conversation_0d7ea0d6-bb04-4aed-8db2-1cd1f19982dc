package org.jeecg.modules.inz_coach_class_student.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.inz_coach_class.entity.InzCoachClass;
import org.jeecg.modules.inz_coach_class.service.IInzCoachClassService;
import org.jeecg.modules.inz_coach_class_student.dto.InzCoachClassStudentDTO;
import org.jeecg.modules.inz_coach_class_student.entity.InzCoachClassStudent;
import org.jeecg.modules.inz_coach_class_student.service.IInzCoachClassStudentService;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 班级学生表
 * @Author: jeecg-boot
 * @Date: 2025-08-02
 * @Version: V1.0
 */
@Api(tags = "班级学生表")
@RestController
@RequestMapping("/inz_coach_class_student/inzCoachClassStudent")
@Slf4j
public class InzCoachClassStudentController extends JeecgController<InzCoachClassStudent, IInzCoachClassStudentService> {
    @Autowired
    private IInzCoachClassStudentService inzCoachClassStudentService;

    @Autowired
    private IInzCoachClassService inzCoachClassService;

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    /**
     * 分页列表查询
     *
     * @param inzCoachClassStudent
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "班级学生表-分页列表查询")
    @AutoLog(value = "班级学生-分页列表查询")
    @ApiOperation(value = "班级学生-分页列表查询", notes = "班级学生-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzCoachClassStudent>> queryPageList(InzCoachClassStudent inzCoachClassStudent,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有coach角色
        String userRoles = loginUser.getRoleCode();
        if (!userRoles.equals("coach") && !userRoles.equals("admin")) {
            return Result.error("您没有权限");
        }

        QueryWrapper<InzCoachClassStudent> queryWrapper = QueryGenerator.initQueryWrapper(inzCoachClassStudent, req.getParameterMap());

        // 如果指定了班级ID，需要验证是否是当前教练的班级
        if (inzCoachClassStudent.getClassId() != null) {
            InzCoachClass coachClass = inzCoachClassService.getById(inzCoachClassStudent.getClassId());
            if (coachClass == null || !coachClass.getCoachId().equals(loginUser.getId())) {
                return Result.error("您只能查看自己班级的学生");
            }
        } else {
            // 如果没有指定班级ID，则查询当前教练所有班级的学生
            QueryWrapper<InzCoachClass> classQuery = new QueryWrapper<>();
            classQuery.eq("coach_id", loginUser.getId());
            List<InzCoachClass> classList = inzCoachClassService.list(classQuery);
            if (classList.isEmpty()) {
                return Result.OK(new Page<>(pageNo, pageSize)); // 返回空页面
            }
            String[] classIds = classList.stream().map(InzCoachClass::getId).toArray(String[]::new);
            queryWrapper.in("class_id", Arrays.asList(classIds));
        }

        queryWrapper.orderByDesc("join_time");

        Page<InzCoachClassStudent> page = new Page<InzCoachClassStudent>(pageNo, pageSize);
        IPage<InzCoachClassStudent> pageList = inzCoachClassStudentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "添加反馈", notes = "添加反馈")
    @PostMapping(value = "/updateFeedback")
    public Result<String> updateFeedback(@RequestBody InzCoachClassStudentDTO inzCoachClassStudent) {
        LambdaQueryWrapper<InzCoachClassStudent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzCoachClassStudent::getStudentId, inzCoachClassStudent.getId());
        InzCoachClassStudent userFront = inzCoachClassStudentService.getOne(queryWrapper);
        if (userFront == null) {
            return Result.error("未找到对应数据");
        }
        userFront.setFeedback(inzCoachClassStudent.getFeedback());
        inzCoachClassStudentService.update(userFront, queryWrapper);
        return Result.OK("添加成功!");
    }

    @ApiOperation(value = "班级学生", notes = "班级下的学生")
    @GetMapping(value = "/getClassStudent")
    public Result<List<InzCoachClassStudent>> getClassStudent(@RequestParam(name = "userId", required = true) String userId) {
        // 1. 查询该教练负责的所有班级
        LambdaQueryWrapper<InzCoachClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzCoachClass::getCoachId, userId);
        List<InzCoachClass> classList = inzCoachClassService.list(queryWrapper);

        if (classList == null || classList.isEmpty()) {
            // 如果教练没有负责任何班级，返回空列表
            return Result.OK(new ArrayList<>());
        }

        // 2. 获取这些班级的ID集合
        List<String> classIds = classList.stream()
                .map(InzCoachClass::getId)
                .collect(Collectors.toList());

        // 3. 查询属于这些班级的所有学生
        LambdaQueryWrapper<InzCoachClassStudent> studentQueryWrapper = new LambdaQueryWrapper<>();
        studentQueryWrapper.in(InzCoachClassStudent::getClassId, classIds);
        List<InzCoachClassStudent> studentList = inzCoachClassStudentService.list(studentQueryWrapper);

        return Result.OK(studentList);
    }

    /**
     * 添加
     *
     * @param inzCoachClassStudent
     * @return
     */
    @AutoLog(value = "班级学生表-添加")
    @ApiOperation(value = "班级学生表-添加", notes = "班级学生表-添加")
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzCoachClassStudent inzCoachClassStudent) {
        inzCoachClassStudentService.save(inzCoachClassStudent);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzCoachClassStudent
     * @return
     */
    @AutoLog(value = "班级学生表-编辑")
    @ApiOperation(value = "班级学生表-编辑", notes = "班级学生表-编辑")
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzCoachClassStudent inzCoachClassStudent) {
        inzCoachClassStudentService.updateById(inzCoachClassStudent);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "班级学生表-通过id删除")
    @ApiOperation(value = "班级学生表-通过id删除", notes = "班级学生表-通过id删除")
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzCoachClassStudentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "班级学生表-批量删除")
    @ApiOperation(value = "班级学生表-批量删除", notes = "班级学生表-批量删除")
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzCoachClassStudentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "班级学生表-通过id查询")
    @ApiOperation(value = "班级学生表-通过id查询", notes = "班级学生表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzCoachClassStudent> queryById(@RequestParam(name = "id", required = true) String id) {
        InzCoachClassStudent inzCoachClassStudent = inzCoachClassStudentService.getById(id);
        if (inzCoachClassStudent == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzCoachClassStudent);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzCoachClassStudent
     */
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCoachClassStudent inzCoachClassStudent) {
        return super.exportXls(request, inzCoachClassStudent, InzCoachClassStudent.class, "班级学生表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_coach_class_student:inz_coach_class_student:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCoachClassStudent.class);
    }

}
