package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;

import java.util.List;

/**
 * @Description: 用户试用记录Mapper
 * @Author: <PERSON> (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Mapper
public interface InzUserTrialLogMapper extends BaseMapper<InzUserTrialLog> {

    /**
     * 根据用户ID查询试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<InzUserTrialLog> selectByUserId(@Param("userId") String userId);

    /**
     * 统计用户已使用的试用天数
     * 注意：trial_days为正数表示增加，负数表示减少，所以直接求和即可得到净使用量
     */
    @Select("SELECT COALESCE(SUM(trial_days), 0) FROM inz_user_trial_log WHERE user_id = #{userId}")
    Integer getTotalUsedDays(@Param("userId") String userId);

    /**
     * 获取用户最新的试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT 1")
    InzUserTrialLog getLatestRecord(@Param("userId") String userId);

    /**
     * 根据操作者ID查询试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE operator_id = #{operatorId} ORDER BY create_time DESC")
    List<InzUserTrialLog> selectByOperatorId(@Param("operatorId") String operatorId);

    /**
     * 统计指定时间范围内的试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<InzUserTrialLog> selectByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
