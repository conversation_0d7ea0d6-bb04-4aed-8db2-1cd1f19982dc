package org.jeecg.modules.system.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.UploadTask;
import org.jeecg.modules.system.service.IChunkUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 分片上传控制器
 * <AUTHOR>
 * @date 2025-08-04
 */
@Api(tags = "分片上传管理")
@RestController
@RequestMapping("/sys/chunk-upload")
@Slf4j
public class ChunkUploadController {

    @Autowired
    private IChunkUploadService chunkUploadService;

    @ApiOperation(value = "初始化分片上传任务", notes = "创建分片上传任务，返回任务ID")
    @PostMapping("/init")
    public Result<UploadTask> initUploadTask(
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName,
            @ApiParam(value = "文件大小(字节)", required = true) @RequestParam Long fileSize,
            @ApiParam(value = "文件MD5哈希值") @RequestParam(required = false) String fileHash,
            @ApiParam(value = "分片大小(字节)，默认5MB") @RequestParam(required = false) Integer chunkSize,
            @ApiParam(value = "业务路径") @RequestParam(required = false) String bizPath,
            @ApiParam(value = "上传类型：local/minio/alioss") @RequestParam(required = false) String uploadType) {
        
        return chunkUploadService.initUploadTask(fileName, fileSize, fileHash, chunkSize, bizPath, uploadType);
    }

    @ApiOperation(value = "上传分片", notes = "上传单个分片文件")
    @PostMapping("/upload-chunk")
    public Result<String> uploadChunk(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId,
            @ApiParam(value = "分片索引", required = true) @RequestParam Integer chunkIndex,
            @ApiParam(value = "分片文件", required = true) @RequestParam("file") MultipartFile chunkFile,
            @ApiParam(value = "分片MD5哈希值") @RequestParam(required = false) String chunkHash) {
        
        return chunkUploadService.uploadChunk(taskId, chunkIndex, chunkFile, chunkHash);
    }

    @ApiOperation(value = "合并分片", notes = "合并所有分片为最终文件")
    @PostMapping("/merge")
    public Result<String> mergeChunks(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId) {
        
        return chunkUploadService.mergeChunks(taskId);
    }

    @ApiOperation(value = "检查上传状态", notes = "获取上传任务的详细状态信息")
    @GetMapping("/status/{taskId}")
    public Result<Map<String, Object>> checkUploadStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        return chunkUploadService.checkUploadStatus(taskId);
    }

    @ApiOperation(value = "获取已上传分片", notes = "获取已成功上传的分片索引列表")
    @GetMapping("/uploaded-chunks/{taskId}")
    public Result<List<Integer>> getUploadedChunks(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        return chunkUploadService.getUploadedChunks(taskId);
    }

    @ApiOperation(value = "获取上传进度", notes = "获取实时上传进度信息")
    @GetMapping("/progress/{taskId}")
    public Result<Map<String, Object>> getUploadProgress(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        return chunkUploadService.getUploadProgress(taskId);
    }

    @ApiOperation(value = "秒传检查", notes = "检查文件是否已存在，支持秒传")
    @PostMapping("/check-instant")
    public Result<String> checkInstantUpload(
            @ApiParam(value = "文件MD5哈希值", required = true) @RequestParam String fileHash,
            @ApiParam(value = "文件大小(字节)") @RequestParam(required = false) Long fileSize) {
        
        return chunkUploadService.checkInstantUpload(fileHash, fileSize);
    }

    @ApiOperation(value = "取消上传", notes = "取消上传任务并清理相关文件")
    @PostMapping("/cancel")
    public Result<String> cancelUpload(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId) {
        
        return chunkUploadService.cancelUpload(taskId);
    }

    @ApiOperation(value = "重试失败分片", notes = "重新设置失败分片状态，允许重新上传")
    @PostMapping("/retry")
    public Result<String> retryFailedChunks(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId) {
        
        return chunkUploadService.retryFailedChunks(taskId);
    }

    @ApiOperation(value = "暂停上传", notes = "暂停上传任务")
    @PostMapping("/pause")
    public Result<String> pauseUpload(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId) {
        
        return chunkUploadService.pauseUpload(taskId);
    }

    @ApiOperation(value = "恢复上传", notes = "恢复暂停的上传任务")
    @PostMapping("/resume")
    public Result<String> resumeUpload(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId) {
        
        return chunkUploadService.resumeUpload(taskId);
    }

    @ApiOperation(value = "验证分片完整性", notes = "检查所有分片的完整性")
    @GetMapping("/validate/{taskId}")
    public Result<Map<String, Object>> validateChunks(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        return chunkUploadService.validateChunks(taskId);
    }

    @ApiOperation(value = "获取用户上传任务", notes = "获取当前用户的上传任务列表")
    @GetMapping("/user-tasks")
    public Result<List<UploadTask>> getUserUploadTasks(
            @ApiParam(value = "状态过滤") @RequestParam(required = false) String status,
            @ApiParam(value = "限制数量") @RequestParam(required = false) Integer limit) {
        
        return chunkUploadService.getUserUploadTasks(status, limit);
    }

    @ApiOperation(value = "删除上传任务", notes = "删除上传任务及相关文件")
    @DeleteMapping("/{taskId}")
    public Result<String> deleteUploadTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        return chunkUploadService.deleteUploadTask(taskId);
    }

    @ApiOperation(value = "获取上传统计", notes = "获取用户的上传统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUploadStatistics(
            @ApiParam(value = "用户ID") @RequestParam(required = false) String userId) {
        
        return chunkUploadService.getUploadStatistics(userId);
    }

    @ApiOperation(value = "清理过期任务", notes = "清理过期的上传任务(管理员功能)")
    @PostMapping("/clean-expired")
    public Result<String> cleanExpiredTasks() {
        return chunkUploadService.cleanExpiredTasks();
    }

    @ApiOperation(value = "兼容性上传接口", notes = "兼容原有上传接口，自动判断是否使用分片上传")
    @PostMapping("/compatible-upload")
    public Result<?> compatibleUpload(HttpServletRequest request) {
        try {
            // 获取参数
            String bizPath = request.getParameter("biz");
            
            // 这里可以添加文件大小判断逻辑
            // 如果文件大小超过阈值（如10MB），自动使用分片上传
            // 否则使用原有的普通上传逻辑
            
            // 暂时返回提示信息
            return Result.ok("请使用分片上传接口进行大文件上传");
            
        } catch (Exception e) {
            log.error("兼容性上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }
}
