package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.vo.InzUserPayLogVO;

import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface InzUserPayLogMapper extends BaseMapper<InzUserPayLog> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<InzUserPayLog>
   */
	public List<InzUserPayLog> selectByMainId(@Param("mainId") String mainId);

	/**
	 * 分页查询增强的金豆记录（包含用户信息）
	 *
	 * @param page 分页参数
	 * @param userId 用户ID
	 * @param phone 手机号
	 * @param realName 姓名
	 * @return IPage<InzUserPayLogVO>
	 */
	public IPage<InzUserPayLogVO> selectEnhancedPayLogPage(Page<InzUserPayLogVO> page,
	                                                       @Param("userId") String userId,
	                                                       @Param("phone") String phone,
	                                                       @Param("realName") String realName);

	/**
	 * 查询指定用户的增强金豆记录
	 *
	 * @param userId 用户ID
	 * @param limit 限制条数
	 * @return List<InzUserPayLogVO>
	 */
	public List<InzUserPayLogVO> selectEnhancedPayLogByUserId(@Param("userId") String userId,
	                                                          @Param("limit") Integer limit);
}
