package org.jeecg.modules.user_front.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@ApiModel(value="inz_user_pay_log对象", description="用户金豆记录")
@Data
@TableName("inz_user_pay_log")
public class InzUserPayLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**用户id*/
    @ApiModelProperty(value = "用户id")
    private String userId;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String content;
	/**金豆数量*/
	@Excel(name = "金豆数量", width = 15)
    @ApiModelProperty(value = "金豆数量")
    private Integer goldenBean;
	/**类型 1增加 0减少*/
	@Excel(name = "类型 1增加 0减少", width = 15)
    @ApiModelProperty(value = "类型 1增加 0减少")
    private Integer type;

    /**来源用户id*/
    @ApiModelProperty(value = "来源用户id")
    private String sourceUserId;

    /**操作前余额*/
    @Excel(name = "操作前余额", width = 15)
    @ApiModelProperty(value = "操作前余额")
    private Integer beforeBalance;

    /**操作后余额*/
    @Excel(name = "操作后余额", width = 15)
    @ApiModelProperty(value = "操作后余额")
    private Integer afterBalance;

    /**权限开通记录ID*/
    @Excel(name = "权限开通记录ID", width = 20)
    @ApiModelProperty(value = "权限开通记录ID（关联inz_permission_grant_log表的id字段）")
    private String grantRecordId;
}
