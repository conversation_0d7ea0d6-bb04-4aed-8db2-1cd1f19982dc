package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.user_front.entity.InzRefundLog;

import java.util.List;

/**
 * @Description: 退单记录Mapper
 * @Author: <PERSON> (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Mapper
public interface InzRefundLogMapper extends BaseMapper<InzRefundLog> {

    /**
     * 统计用户退单次数
     * @param userId 用户ID
     * @return 退单次数
     */
    @Select("SELECT COUNT(*) FROM inz_refund_log WHERE user_id = #{userId} AND status = 'completed'")
    Integer countRefundsByUserId(@Param("userId") String userId);

    /**
     * 查询用户最新的退单记录
     * @param userId 用户ID
     * @return 最新退单记录
     */
    @Select("SELECT * FROM inz_refund_log WHERE user_id = #{userId} AND status = 'completed' ORDER BY create_time DESC LIMIT 1")
    InzRefundLog selectLatestRefundByUserId(@Param("userId") String userId);

    /**
     * 查询用户所有退单记录
     * @param userId 用户ID
     * @return 退单记录列表
     */
    @Select("SELECT * FROM inz_refund_log WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<InzRefundLog> selectRefundsByUserId(@Param("userId") String userId);

    /**
     * 统计操作者处理的退单数量
     * @param operatorId 操作者ID
     * @return 退单数量
     */
    @Select("SELECT COUNT(*) FROM inz_refund_log WHERE operator_id = #{operatorId} AND status = 'completed'")
    Integer countRefundsByOperatorId(@Param("operatorId") String operatorId);

    /**
     * 统计操作者处理的退单总金币
     * @param operatorId 操作者ID
     * @return 退单总金币
     */
    @Select("SELECT COALESCE(SUM(refund_amount), 0) FROM inz_refund_log WHERE operator_id = #{operatorId} AND status = 'completed'")
    Integer sumRefundAmountByOperatorId(@Param("operatorId") String operatorId);
}
