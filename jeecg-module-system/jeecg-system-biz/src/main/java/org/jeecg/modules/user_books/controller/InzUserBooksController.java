package org.jeecg.modules.user_books.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.education.service.IInzEducationService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.user_books.entity.*;
import org.jeecg.modules.user_books.service.IInzUserBooksService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 用户选择图书管理
 * @Author: jeecg-boot
 * @Date: 2025-03-29
 * @Version: V1.0
 */
@Api(tags = "用户选择图书管理")
@RestController
@RequestMapping("/user_books/inzUserBooks")
@Slf4j
public class InzUserBooksController extends JeecgController<InzUserBooks, IInzUserBooksService> {
    @Autowired
    private IInzUserBooksService inzUserBooksService;
    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Autowired
    private IInzEducationService inzEducationService;

    @Autowired
    private IInzWordBooksService inzWordBooksService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 分页列表查询
     *
     * @param inzUserBooks
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "用户选择图书管理-分页列表查询")
    @ApiOperation(value = "用户选择图书管理-分页列表查询", notes = "用户选择图书管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzUserBooks>> queryPageList(InzUserBooks inzUserBooks,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<InzUserBooks> queryWrapper = QueryGenerator.initQueryWrapper(inzUserBooks, req.getParameterMap());
        Page<InzUserBooks> page = new Page<InzUserBooks>(pageNo, pageSize);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if ("test".equals(sysUser.getRoleCode())) {
            InzUserFront userFront = inzUserFrontService.getOne(new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getUsername()));

            // 1. 先查询所有用户数据（可根据业务需要加.where条件）
            List<InzUserFront> allUsers = inzUserFrontService.list();

            // 2. 构建用户ID -> 下级ID列表的映射（自动跳过parentId=null的记录）
            Map<String, List<String>> userSubordinatesMap = allUsers.stream()
                    .filter(user -> user.getParentId() != null && !user.getParentId().isEmpty())
                    .collect(Collectors.groupingBy(
                            InzUserFront::getParentId,
                            Collectors.mapping(InzUserFront::getId, Collectors.toList())
                    ));

            // 3. 如果当前用户没有下级，直接返回空
            if (!userSubordinatesMap.containsKey(userFront.getId())) {
                return Result.OK(new Page<>(pageNo, pageSize, 0));
            }

            // 4. 递归查找所有下级ID
            List<String> allSubordinateIds = new ArrayList<>();
            allSubordinateIds.add(userFront.getId());
            findSubordinates(userFront.getId(), userSubordinatesMap, allSubordinateIds);

            // 4. 添加到查询条件
            queryWrapper.lambda().in(InzUserBooks::getCreateBy, allSubordinateIds);
        }
        IPage<InzUserBooks> pageList = inzUserBooksService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    // 递归查找方法
    private void findSubordinates(String userId,
                                  Map<String, List<String>> userSubordinatesMap,
                                  List<String> result) {
        List<String> directSubordinates = userSubordinatesMap.getOrDefault(userId, Collections.emptyList());
        for (String subId : directSubordinates) {
            result.add(subId);
            findSubordinates(subId, userSubordinatesMap, result); // 递归
        }
    }

    @ApiOperation(value = "用户选择图书管理-分页列表查询", notes = "用户选择图书管理-分页列表查询")
    @GetMapping(value = "/listAll")
    public Result<List<InzUserBooks>> listAll(InzWordBooks inzWordBooks,
                                              HttpServletRequest req) {
        List<InzUserBooks> pageList = inzUserBooksService.list();
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param addUserBooksDto
     * @return
     */
    @AutoLog(value = "用户选择图书管理-添加")
    @ApiOperation(value = "用户选择图书管理-添加", notes = "用户选择图书管理-添加")
    @RequiresPermissions("user_books:inz_user_books:add")
    @PostMapping(value = "/add")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> assignBooks(@Valid @RequestBody AddUserBooksDto addUserBooksDto) throws Exception {
        String loginUserId = CommonUtils.getUserIdByToken();
        SysUser sysUser = sysUserService.getById(loginUserId);
        InzUserFront userFront = inzUserFrontService.getOne(new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getUsername()));
        // 1. 参数预处理
        List<String> userIds = Arrays.asList(addUserBooksDto.getUserIds().split(","));
        List<String> bookIds = Arrays.asList(addUserBooksDto.getWordBookIds().split(","));

        // 2. 获取图书金豆信息
        Map<String, Integer> bookPriceMap = inzWordBooksService.listByIds(bookIds).stream()
                .collect(Collectors.toMap(InzWordBooks::getId, b -> b.getGoldenBean() != null ? b.getGoldenBean() : 0));

        // 3. 检查已有分配记录（优化查询）
        List<Pair<String, String>> existPairs = inzUserBooksService.lambdaQuery()
                .select(InzUserBooks::getCreateBy, InzUserBooks::getWordBookId)
                .in(InzUserBooks::getCreateBy, userIds)
                .in(InzUserBooks::getWordBookId, bookIds)
                .list()
                .stream()
                .map(r -> Pair.of(r.getCreateBy(), r.getWordBookId()))
                .collect(Collectors.toList());
        // 4. 计算实际需扣金豆
        int totalCost = 0;
        List<InzUserBooks> newRecords = new ArrayList<>();
        for (String userId : userIds) {
            for (String bookId : bookIds) {
                if (!existPairs.contains(Pair.of(userId, bookId))) {
                    totalCost += bookPriceMap.getOrDefault(bookId, 0);
                    newRecords.add(buildUserBook(userId, bookId, addUserBooksDto, userFront));
                }
            }
        }
        // 5. 金豆扣减与记录保存
        if (totalCost > 0) {
            inzUserFrontService.verifyAndDeduct(userFront.getId(), totalCost, addUserBooksDto, newRecords.size());
            inzUserBooksService.saveBatch(newRecords);
        }
        return Result.OK(buildResultMessage(newRecords.size(), existPairs.size(), totalCost));
    }

    private InzUserBooks buildUserBook(String userId, String bookId, AddUserBooksDto dto, InzUserFront userFront) {
        return new InzUserBooks()
                .setId(IdWorker.getIdStr())
                .setCreateBy(userId)
                .setCreateTime(new Date())
                .setUpdateBy(userFront.getId())
                .setUpdateTime(new Date())
                .setWordBookId(bookId)
                .setStatus(2)
                .setExpirationStartData(dto.getTimeRange()[0])
                .setExpirationEndData(dto.getTimeRange()[1]);
    }

    private String buildResultMessage(int added, int skipped, int cost) {
        return String.format("成功添加%d本，跳过%d本重复，扣减%d金豆", added, skipped, cost);
    }

    /**
     * 编辑
     *
     * @param inzUserBooks
     * @return
     */
    @AutoLog(value = "用户选择图书管理-编辑")
    @ApiOperation(value = "用户选择图书管理-编辑", notes = "用户选择图书管理-编辑")
    @RequiresPermissions("user_books:inz_user_books:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzUserBooks inzUserBooks) {
        inzUserBooksService.updateById(inzUserBooks);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "用户选择图书管理-通过id删除")
    @ApiOperation(value = "用户选择图书管理-通过id删除", notes = "用户选择图书管理-通过id删除")
    @RequiresPermissions("user_books:inz_user_books:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzUserBooksService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "用户选择图书管理-批量删除")
    @ApiOperation(value = "用户选择图书管理-批量删除", notes = "用户选择图书管理-批量删除")
    @RequiresPermissions("user_books:inz_user_books:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzUserBooksService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户选择图书管理-通过id查询")
    @ApiOperation(value = "用户选择图书管理-通过id查询", notes = "用户选择图书管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzUserBooks> queryById(@RequestParam(name = "id", required = true) String id) {
        InzUserBooks inzUserBooks = inzUserBooksService.getById(id);
        if (inzUserBooks == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzUserBooks);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzUserBooks
     */
    @RequiresPermissions("user_books:inz_user_books:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzUserBooks inzUserBooks) {
        return super.exportXls(request, inzUserBooks, InzUserBooks.class, "用户选择图书管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("user_books:inz_user_books:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzUserBooks.class);
    }

    @AutoLog(value = "用户选择图书-停用")
    @PutMapping("/disable")
    public Result<String> disable(@RequestBody Map<String, String> params) {
        String id = params.get("id");
        // 实现停用逻辑
        InzUserBooks inzUserBooks = new InzUserBooks();
        inzUserBooks.setStatus(3);
        inzUserBooksService.update(inzUserBooks, new QueryWrapper<InzUserBooks>().lambda().eq(InzUserBooks::getId, id));
        return Result.OK("停用成功");
    }

    @AutoLog(value = "用户选择图书-批量状态切换")
    @PutMapping("/batchToggleStatus")
    public Result<String> batchToggleStatus(@RequestBody Map<String, List<String>> params) {
        List<String> ids = params.get("ids");

        // 先查询出所有符合条件的记录
        List<InzUserBooks> records = inzUserBooksService.list(
                new QueryWrapper<InzUserBooks>()
                        .lambda()
                        .in(InzUserBooks::getId, ids)
        );
        // 分批处理更新
        records.forEach(record -> {
            InzUserBooks updateEntity = new InzUserBooks();
            if (record.getStatus() == 1 || record.getStatus() == 2) {
                // 当前是启用状态(1/2)，则停用(3)
                updateEntity.setStatus(3);
            } else if (record.getStatus() == 3) {
                // 当前是停用状态(3)，则启用(2)
                updateEntity.setStatus(2);
            }
            if (updateEntity.getStatus() != null) {
                inzUserBooksService.update(updateEntity,
                        new QueryWrapper<InzUserBooks>()
                                .lambda()
                                .eq(InzUserBooks::getId, record.getId())
                );
            }
        });
        return Result.OK("批量状态切换成功");
    }

    @AutoLog(value = "用户图书-单个启用")
    @ApiOperation(value = "用户图书-单个启用", notes = "启用单个用户图书关系")
    @PutMapping("/enable")
    public Result<String> enable(@RequestBody @Valid EnableDTO dto) {
        InzUserBooks inzUserBooks = new InzUserBooks();
        inzUserBooks.setStatus(2);
        inzUserBooksService.update(inzUserBooks, new QueryWrapper<InzUserBooks>().lambda().eq(InzUserBooks::getId, dto.getId()));
        return Result.OK("启用成功");
    }

    @AutoLog(value = "用户图书-批量启用")
    @ApiOperation(value = "用户图书-批量启用", notes = "批量启用用户图书关系")
    @PutMapping("/batchEnable")
    public Result<String> batchEnable(@RequestBody @Valid BatchEnableDTO dto) {
        InzUserBooks inzUserBooks = new InzUserBooks();
        inzUserBooks.setStatus(2);
        inzUserBooksService.update(inzUserBooks, new QueryWrapper<InzUserBooks>().lambda().in(InzUserBooks::getId, dto.getIds()));
        return Result.OK("批量启用成功");
    }

    @AutoLog(value = "开通单词书权限")
    @ApiOperation(value = "开通单词书权限", notes = "支持创总、渠道、合伙人开通单词书体验权限和全年权限")
    @PostMapping(value = "/adminOpenBook")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> adminOpenBook(@Valid @RequestBody AdminOpenBookDTO adminOpenBookDTO) {
        try {
            // 获取当前登录用户
            String loginUserId = CommonUtils.getUserIdByToken();
            SysUser sysUser = sysUserService.getById(loginUserId);
            InzUserFront userFront = inzUserFrontService.getOne(new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getUsername()));

            // 验证当前用户是否有权限（创总、渠道、合伙人）
            if (userFront == null || !isAdmin(userFront.getRole())) {
                return Result.error("您没有开通权限的权限");
            }

            // 获取单词书信息
            InzWordBooks wordBook = inzWordBooksService.getById(adminOpenBookDTO.getBookId());
            if (wordBook == null) {
                return Result.error("单词书不存在");
            }

            // 设置开通权限的时间范围
            String[] timeRange = getTimeRangeByPermissionType(adminOpenBookDTO.getPermissionType());

            // 检查已有分配记录（优化查询）
            List<Pair<String, String>> existPairs = inzUserBooksService.lambdaQuery()
                    .select(InzUserBooks::getCreateBy, InzUserBooks::getWordBookId)
                    .in(InzUserBooks::getCreateBy, adminOpenBookDTO.getUserIds())
                    .eq(InzUserBooks::getWordBookId, adminOpenBookDTO.getBookId())
                    .list()
                    .stream()
                    .map(r -> Pair.of(r.getCreateBy(), r.getWordBookId()))
                    .collect(Collectors.toList());

            // 计算需要新增的记录
            List<InzUserBooks> newRecords = new ArrayList<>();
            for (String userId : adminOpenBookDTO.getUserIds()) {
                if (!existPairs.contains(Pair.of(userId, adminOpenBookDTO.getBookId()))) {
                    newRecords.add(buildUserBookForAdmin(userId, adminOpenBookDTO.getBookId(), timeRange, userFront));
                }
            }

            // 批量保存新记录
            if (!newRecords.isEmpty()) {
                inzUserBooksService.saveBatch(newRecords);
            }

            return Result.OK(String.format("成功为%d个用户开通%s权限，跳过%d个已开通用户",
                    newRecords.size(),
                    adminOpenBookDTO.getPermissionType() == 1 ? "体验" : "全年",
                    existPairs.size()));
        } catch (Exception e) {
            log.error("管理员开通单词书权限失败", e);
            return Result.error("开通失败：" + e.getMessage());
        }
    }

    /**
     * 构建用户单词书关系记录（管理员开通）
     */
    private InzUserBooks buildUserBookForAdmin(String userId, String bookId, String[] timeRange, InzUserFront admin) {
        return new InzUserBooks()
                .setId(IdWorker.getIdStr())
                .setCreateBy(userId)
                .setCreateTime(new Date())
                .setUpdateBy(admin.getId())
                .setUpdateTime(new Date())
                .setWordBookId(bookId)
                .setStatus(2)  // 默认为启用状态
                .setExpirationStartData(timeRange[0])
                .setExpirationEndData(timeRange[1]);
    }

    /**
     * 根据权限类型获取时间范围
     */
    private String[] getTimeRangeByPermissionType(Integer permissionType) {
        String[] timeRange = new String[2];
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        timeRange[0] = sdf.format(now);

        // 计算结束时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        if (permissionType == 1) {
            // 体验权限，7天
            calendar.add(Calendar.DAY_OF_MONTH, 7);
        } else {
            // 全年权限，一年
            calendar.add(Calendar.YEAR, 1);
        }
        timeRange[1] = sdf.format(calendar.getTime());

        return timeRange;
    }

    /**
     * 判断用户是否有管理员权限（创总、渠道、合伙人）
     */
    private boolean isAdmin(String role) {
        return "chuang".equals(role) || "channel".equals(role) || "partner".equals(role);
    }

}
