package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.system.entity.UploadChunk;

import java.util.List;

/**
 * 分片上传详情Mapper
 * <AUTHOR>
 * @date 2025-08-04
 */
@Mapper
public interface UploadChunkMapper extends BaseMapper<UploadChunk> {

    /**
     * 根据任务ID查找所有分片
     * @param taskId 任务ID
     * @return 分片列表
     */
    @Select("SELECT * FROM upload_chunk WHERE task_id = #{taskId} ORDER BY chunk_index")
    List<UploadChunk> findByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID和分片索引查找分片
     * @param taskId 任务ID
     * @param chunkIndex 分片索引
     * @return 分片信息
     */
    @Select("SELECT * FROM upload_chunk WHERE task_id = #{taskId} AND chunk_index = #{chunkIndex}")
    UploadChunk findByTaskIdAndChunkIndex(@Param("taskId") String taskId, @Param("chunkIndex") Integer chunkIndex);

    /**
     * 查找任务的已完成分片
     * @param taskId 任务ID
     * @return 已完成分片列表
     */
    @Select("SELECT * FROM upload_chunk WHERE task_id = #{taskId} AND status = 'COMPLETED' ORDER BY chunk_index")
    List<UploadChunk> findCompletedChunksByTaskId(@Param("taskId") String taskId);

    /**
     * 查找任务的失败分片
     * @param taskId 任务ID
     * @return 失败分片列表
     */
    @Select("SELECT * FROM upload_chunk WHERE task_id = #{taskId} AND status = 'FAILED' ORDER BY chunk_index")
    List<UploadChunk> findFailedChunksByTaskId(@Param("taskId") String taskId);

    /**
     * 统计任务的已完成分片数量
     * @param taskId 任务ID
     * @return 已完成分片数量
     */
    @Select("SELECT COUNT(*) FROM upload_chunk WHERE task_id = #{taskId} AND status = 'COMPLETED'")
    int countCompletedChunksByTaskId(@Param("taskId") String taskId);

    /**
     * 统计任务的失败分片数量
     * @param taskId 任务ID
     * @return 失败分片数量
     */
    @Select("SELECT COUNT(*) FROM upload_chunk WHERE task_id = #{taskId} AND status = 'FAILED'")
    int countFailedChunksByTaskId(@Param("taskId") String taskId);

    /**
     * 更新分片状态
     * @param chunkId 分片ID
     * @param status 新状态
     * @param storagePath 存储路径(可选)
     * @param errorMessage 错误信息(可选)
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE upload_chunk SET status = #{status}, updated_time = NOW() " +
            "<if test='storagePath != null'>" +
            ", storage_path = #{storagePath} " +
            "</if>" +
            "<if test='errorMessage != null'>" +
            ", error_message = #{errorMessage} " +
            "</if>" +
            "WHERE id = #{chunkId}" +
            "</script>")
    int updateChunkStatus(@Param("chunkId") String chunkId, @Param("status") String status, 
                         @Param("storagePath") String storagePath, @Param("errorMessage") String errorMessage);

    /**
     * 增加分片重试次数
     * @param chunkId 分片ID
     * @return 更新行数
     */
    @Update("UPDATE upload_chunk SET retry_count = IFNULL(retry_count, 0) + 1, updated_time = NOW() WHERE id = #{chunkId}")
    int incrementRetryCount(@Param("chunkId") String chunkId);

    /**
     * 批量插入分片记录
     * @param chunks 分片列表
     * @return 插入行数
     */
    int batchInsertChunks(@Param("chunks") List<UploadChunk> chunks);

    /**
     * 删除任务的所有分片
     * @param taskId 任务ID
     * @return 删除行数
     */
    @Update("DELETE FROM upload_chunk WHERE task_id = #{taskId}")
    int deleteByTaskId(@Param("taskId") String taskId);

    /**
     * 查找可重试的失败分片
     * @param taskId 任务ID
     * @param maxRetryCount 最大重试次数
     * @return 可重试的分片列表
     */
    @Select("SELECT * FROM upload_chunk WHERE task_id = #{taskId} AND status = 'FAILED' " +
            "AND (retry_count IS NULL OR retry_count < #{maxRetryCount}) ORDER BY chunk_index")
    List<UploadChunk> findRetryableChunksByTaskId(@Param("taskId") String taskId, @Param("maxRetryCount") Integer maxRetryCount);

    /**
     * 检查任务是否有未完成的分片
     * @param taskId 任务ID
     * @return 未完成分片数量
     */
    @Select("SELECT COUNT(*) FROM upload_chunk WHERE task_id = #{taskId} AND status != 'COMPLETED'")
    int countIncompleteChunksByTaskId(@Param("taskId") String taskId);
}
