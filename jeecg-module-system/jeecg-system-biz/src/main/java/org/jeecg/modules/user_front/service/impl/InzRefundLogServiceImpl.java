package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.user_front.controller.RefundController.RefundHistoryVO;
import org.jeecg.modules.user_front.entity.InzRefundLog;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.mapper.InzRefundLogMapper;
import org.jeecg.modules.user_front.service.IInzRefundLogService;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 退单记录Service实现类
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzRefundLogServiceImpl extends ServiceImpl<InzRefundLogMapper, InzRefundLog> implements IInzRefundLogService {

    @Autowired
    private InzRefundLogMapper refundLogMapper;

    @Autowired
    private IInzUserFrontService userFrontService;

    @Override
    public boolean createRefundRecord(String userId, String operatorId, String permissionRecordId,
                                     String refundReason, Integer refundAmount, Integer originalAmount,
                                     Boolean isSecondRefund, Date permissionStartTime) {
        try {
            log.info("创建退单记录 - 用户: {}, 操作者: {}, 退款金币: {}, 是否二次退单: {}",
                    userId, operatorId, refundAmount, isSecondRefund);

            InzRefundLog refundLog = new InzRefundLog();
            refundLog.setUserId(userId);
            refundLog.setOperatorId(operatorId);
            refundLog.setPermissionRecordId(permissionRecordId);
            refundLog.setRefundReason(refundReason);
            refundLog.setRefundAmount(refundAmount);
            refundLog.setOriginalAmount(originalAmount);
            refundLog.setIsSecondRefund(isSecondRefund);
            refundLog.setRefundType("annual"); // 全年权限退单
            refundLog.setStatus("completed"); // 已完成
            refundLog.setPermissionStartTime(permissionStartTime);
            refundLog.setRefundRequestTime(new Date());
            refundLog.setCreateTime(new Date());
            refundLog.setCreateBy(operatorId);

            boolean result = this.save(refundLog);
            if (result) {
                log.info("退单记录创建成功 - 记录ID: {}", refundLog.getId());
            } else {
                log.error("退单记录创建失败 - 用户: {}", userId);
            }
            return result;

        } catch (Exception e) {
            log.error("创建退单记录异常 - 用户: {}, 操作者: {}", userId, operatorId, e);
            return false;
        }
    }

    @Override
    public List<RefundHistoryVO> getRefundHistory(String userId) {
        try {
            log.debug("查询用户退单历史 - 用户: {}", userId);

            List<InzRefundLog> refundLogs = refundLogMapper.selectRefundsByUserId(userId);
            List<RefundHistoryVO> historyList = new ArrayList<>();

            for (InzRefundLog refundLog : refundLogs) {
                RefundHistoryVO historyVO = new RefundHistoryVO();
                historyVO.setId(refundLog.getId());
                historyVO.setUserId(refundLog.getUserId());
                historyVO.setOperatorId(refundLog.getOperatorId());
                historyVO.setRefundReason(refundLog.getRefundReason());
                historyVO.setRefundAmount(refundLog.getRefundAmount());
                historyVO.setIsSecondRefund(refundLog.getIsSecondRefund());
                historyVO.setCreateTime(refundLog.getCreateTime());

                // 获取操作者姓名
                try {
                    InzUserFront operator = userFrontService.getByBackendUserId(refundLog.getOperatorId());
                    historyVO.setOperatorName(operator != null ? operator.getRealName() : "未知操作者");
                } catch (Exception e) {
                    log.warn("获取操作者姓名失败 - 操作者ID: {}", refundLog.getOperatorId(), e);
                    historyVO.setOperatorName("未知操作者");
                }

                historyList.add(historyVO);
            }

            log.debug("查询到退单历史记录 {} 条 - 用户: {}", historyList.size(), userId);
            return historyList;

        } catch (Exception e) {
            log.error("查询用户退单历史失败 - 用户: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean hasRefundHistory(String userId) {
        try {
            Integer count = refundLogMapper.countRefundsByUserId(userId);
            boolean hasHistory = count != null && count > 0;
            log.debug("用户退单历史检查 - 用户: {}, 退单次数: {}, 有历史: {}", userId, count, hasHistory);
            return hasHistory;
        } catch (Exception e) {
            log.error("检查用户退单历史失败 - 用户: {}", userId, e);
            return false;
        }
    }

    @Override
    public Integer getRefundCount(String userId) {
        try {
            Integer count = refundLogMapper.countRefundsByUserId(userId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取用户退单次数失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    @Override
    public InzRefundLog getLatestRefund(String userId) {
        try {
            return refundLogMapper.selectLatestRefundByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户最新退单记录失败 - 用户: {}", userId, e);
            return null;
        }
    }
}
