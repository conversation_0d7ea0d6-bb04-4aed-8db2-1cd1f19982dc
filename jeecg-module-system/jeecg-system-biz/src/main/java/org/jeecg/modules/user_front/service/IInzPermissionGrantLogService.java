package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_front.entity.InzPermissionGrantLog;

import java.util.Date;
import java.util.List;

/**
 * @Description: 权限开通记录Service接口
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzPermissionGrantLogService extends IService<InzPermissionGrantLog> {

    /**
     * 创建权限开通记录
     * @param userId 用户ID
     * @param operatorId 操作者ID
     * @param educationId 教育阶段ID
     * @param educationName 教育阶段名称
     * @param grantType 开通类型
     * @param goldenBeanCost 消耗金币数量
     * @param wordBookCount 开通的单词书数量
     * @param grantTime 开通时间
     * @param expirationTime 过期时间
     * @return 是否创建成功
     */
    boolean createGrantRecord(String userId, String operatorId, String educationId, String educationName,
                             String grantType, Integer goldenBeanCost, Integer wordBookCount,
                             Date grantTime, Date expirationTime);

    /**
     * 查询用户的权限开通记录
     * @param userId 用户ID
     * @return 权限开通记录列表
     */
    List<InzPermissionGrantLog> getUserGrantRecords(String userId);

    /**
     * 查询用户有效的权限开通记录
     * @param userId 用户ID
     * @return 有效的权限开通记录列表
     */
    List<InzPermissionGrantLog> getUserActiveGrantRecords(String userId);

    /**
     * 查询用户最新的权限开通记录
     * @param userId 用户ID
     * @return 最新的权限开通记录
     */
    InzPermissionGrantLog getLatestGrantRecord(String userId);

    /**
     * 根据用户ID和教育阶段ID查询权限记录
     * @param userId 用户ID
     * @param educationId 教育阶段ID
     * @return 权限开通记录
     */
    InzPermissionGrantLog getGrantRecordByUserAndEducation(String userId, String educationId);

    /**
     * 更新权限记录状态
     * @param recordId 记录ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 是否更新成功
     */
    boolean updateGrantRecordStatus(String recordId, String status, String updateBy);

    /**
     * 将用户的所有有效权限记录标记为已退单
     * @param userId 用户ID
     * @param updateBy 更新人
     * @return 更新的记录数量
     */
    int markUserPermissionsAsRefunded(String userId, String updateBy);

    /**
     * 将指定教育阶段的权限记录标记为已退单
     * @param userId 用户ID
     * @param educationId 教育阶段ID
     * @param updateBy 更新人
     * @return 是否更新成功
     */
    boolean markEducationPermissionAsRefunded(String userId, String educationId, String updateBy);

    /**
     * 统计操作者的权限开通数量
     * @param operatorId 操作者ID
     * @return 开通数量
     */
    Integer getOperatorGrantCount(String operatorId);

    /**
     * 统计操作者开通权限消耗的总金币
     * @param operatorId 操作者ID
     * @return 总金币数
     */
    Integer getOperatorTotalGoldenBean(String operatorId);

    /**
     * 查询指定时间范围内的权限开通记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 权限开通记录列表
     */
    List<InzPermissionGrantLog> getGrantRecordsByTimeRange(String startTime, String endTime);
}
