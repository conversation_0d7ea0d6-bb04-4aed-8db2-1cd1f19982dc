package org.jeecg.modules.user_front.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 退单记录表
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@TableName("inz_refund_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_refund_log对象", description="退单记录表")
public class InzRefundLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**操作者ID*/
    @Excel(name = "操作者ID", width = 15)
    @ApiModelProperty(value = "操作者ID")
    private String operatorId;

    /**权限记录ID*/
    @Excel(name = "权限记录ID", width = 15)
    @ApiModelProperty(value = "权限记录ID")
    private String permissionRecordId;

    /**退单原因*/
    @Excel(name = "退单原因", width = 30)
    @ApiModelProperty(value = "退单原因")
    private String refundReason;

    /**退款金币数量*/
    @Excel(name = "退款金币数量", width = 15)
    @ApiModelProperty(value = "退款金币数量")
    private Integer refundAmount;

    /**原始金币数量*/
    @Excel(name = "原始金币数量", width = 15)
    @ApiModelProperty(value = "原始金币数量")
    private Integer originalAmount;

    /**是否为二次退单*/
    @Excel(name = "是否为二次退单", width = 15)
    @ApiModelProperty(value = "是否为二次退单")
    private Boolean isSecondRefund;

    /**退单类型*/
    @Excel(name = "退单类型", width = 15)
    @ApiModelProperty(value = "退单类型")
    private String refundType;

    /**退单状态*/
    @Excel(name = "退单状态", width = 15)
    @ApiModelProperty(value = "退单状态")
    private String status;

    /**权限开始时间*/
    @Excel(name = "权限开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "权限开始时间")
    private Date permissionStartTime;

    /**退单申请时间*/
    @Excel(name = "退单申请时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退单申请时间")
    private Date refundRequestTime;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
