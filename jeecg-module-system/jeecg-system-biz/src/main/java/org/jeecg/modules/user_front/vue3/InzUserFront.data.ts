import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '真实姓名',
    align:"center",
    dataIndex: 'realName'
   },
   {
    title: '年级',
    align:"center",
    dataIndex: 'grade'
   },
   {
    title: '手机号',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '密码',
    align:"center",
    dataIndex: 'password'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '名字',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '公众号昵称',
    align:"center",
    dataIndex: 'oaNickname'
   },
   {
    title: '公众号 OpenId',
    align:"center",
    dataIndex: 'oaOpenid'
   },
   {
    title: '小程序OpenId',
    align:"center",
    dataIndex: 'mpOpenid'
   },
   {
    title: '企业微信 OpenId',
    align:"center",
    dataIndex: 'ewOpenid'
   },
   {
    title: '联合 Id',
    align:"center",
    dataIndex: 'unionId'
   },
   {
    title: '角色',
    align:"center",
    dataIndex: 'role'
   },
   {
    title: '状态 (1正常 0停用)',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '头像',
    align:"center",
    dataIndex: 'avatar'
   },
   {
    title: '最后一次进入系统的时间',
    align:"center",
    dataIndex: 'lastUseAt'
   },
   {
    title: '会员时间',
    align:"center",
    dataIndex: 'vipTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
  },
  {
    label: '年级',
    field: 'grade',
    component: 'Input',
  },
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '密码',
    field: 'password',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '名字',
    field: 'name',
    component: 'Input',
  },
  {
    label: '公众号昵称',
    field: 'oaNickname',
    component: 'Input',
  },
  {
    label: '公众号 OpenId',
    field: 'oaOpenid',
    component: 'Input',
  },
  {
    label: '小程序OpenId',
    field: 'mpOpenid',
    component: 'Input',
  },
  {
    label: '企业微信 OpenId',
    field: 'ewOpenid',
    component: 'Input',
  },
  {
    label: '联合 Id',
    field: 'unionId',
    component: 'Input',
  },
  {
    label: '角色',
    field: 'role',
    component: 'Input',
  },
  {
    label: '状态 (1正常 0停用)',
    field: 'status',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'Input',
  },
  {
    label: '最后一次进入系统的时间',
    field: 'lastUseAt',
    component: 'Input',
  },
  {
    label: '会员时间',
    field: 'vipTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
//子表列表数据
export const inzUserDeviceColumns: BasicColumn[] = [
   {
    title: '用户id',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: 'token',
    align:"center",
    dataIndex: 'token'
   },
   {
    title: '过期时间戳',
    align:"center",
    dataIndex: 'exprie'
   },
   {
    title: '设备名称',
    align:"center",
    dataIndex: 'deviceName'
   },
   {
    title: '系统版本名称',
    align:"center",
    dataIndex: 'systemName'
   },
   {
    title: '浏览器版本名称',
    align:"center",
    dataIndex: 'browerName'
   },
   {
    title: '登录时间',
    align:"center",
    dataIndex: 'loginTime'
   },
];
//子表列表数据
export const inzUserPayLogColumns: BasicColumn[] = [
   {
    title: '用户id',
    align:"center",
    dataIndex: 'userId'
   },
   {
    title: '描述',
    align:"center",
    dataIndex: 'content'
   },
   {
    title: '金豆数量',
    align:"center",
    dataIndex: 'goldenBean'
   },
   {
    title: '类型 1增加 0减少',
    align:"center",
    dataIndex: 'type'
   },
];
export const inzUserPayLogFormSchema: FormSchema[] = [
  {
    label: '用户id',
    field: 'userId',
    component: 'Input',
  },
  {
    label: '描述',
    field: 'content',
    component: 'Input',
  },
  {
    label: '金豆数量',
    field: 'goldenBean',
    component: 'InputNumber',
  },
  {
    label: '类型 1增加 0减少',
    field: 'type',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类型 1增加 0减少!'},
          ];
     },
  },
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表表格配置
export const inzUserDeviceJVxeColumns: JVxeColumn[] = [
    {
      title: '用户id',
      key: 'userId',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: 'token',
      key: 'token',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '过期时间戳',
      key: 'exprie',
      type: JVxeTypes.datetime,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '设备名称',
      key: 'deviceName',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '系统版本名称',
      key: 'systemName',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '浏览器版本名称',
      key: 'browerName',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '登录时间',
      key: 'loginTime',
      type: JVxeTypes.datetime,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
  ]

// 高级查询数据
export const superQuerySchema = {
  realName: {title: '真实姓名',order: 0,view: 'text', type: 'string',},
  grade: {title: '年级',order: 1,view: 'text', type: 'string',},
  phone: {title: '手机号',order: 2,view: 'text', type: 'string',},
  password: {title: '密码',order: 3,view: 'text', type: 'string',},
  address: {title: '地址',order: 4,view: 'text', type: 'string',},
  name: {title: '名字',order: 5,view: 'text', type: 'string',},
  oaNickname: {title: '公众号昵称',order: 6,view: 'text', type: 'string',},
  oaOpenid: {title: '公众号 OpenId',order: 7,view: 'text', type: 'string',},
  mpOpenid: {title: '小程序OpenId',order: 8,view: 'text', type: 'string',},
  ewOpenid: {title: '企业微信 OpenId',order: 9,view: 'text', type: 'string',},
  unionId: {title: '联合 Id',order: 10,view: 'text', type: 'string',},
  role: {title: '角色',order: 11,view: 'text', type: 'string',},
  status: {title: '状态 (1正常 0停用)',order: 12,view: 'text', type: 'string',},
  remark: {title: '备注',order: 13,view: 'text', type: 'string',},
  avatar: {title: '头像',order: 14,view: 'text', type: 'string',},
  lastUseAt: {title: '最后一次进入系统的时间',order: 15,view: 'text', type: 'string',},
  vipTime: {title: '会员时间',order: 16,view: 'date', type: 'string',},
  //子表高级查询
  inzUserDevice: {
    title: '用户登录设备',
    view: 'table',
    fields: {
        userId: {title: '用户id',order: 0,view: 'text', type: 'string',},
        token: {title: 'token',order: 1,view: 'text', type: 'string',},
        exprie: {title: '过期时间戳',order: 2,view: 'datetime', type: 'string',},
        deviceName: {title: '设备名称',order: 3,view: 'text', type: 'string',},
        systemName: {title: '系统版本名称',order: 4,view: 'text', type: 'string',},
        browerName: {title: '浏览器版本名称',order: 5,view: 'text', type: 'string',},
        loginTime: {title: '登录时间',order: 6,view: 'datetime', type: 'string',},
    }
  },
  inzUserPayLog: {
    title: '用户金豆记录',
    view: 'table',
    fields: {
        userId: {title: '用户id',order: 0,view: 'text', type: 'string',},
        content: {title: '描述',order: 1,view: 'text', type: 'string',},
        goldenBean: {title: '金豆数量',order: 2,view: 'number', type: 'number',},
        type: {title: '类型 1增加 0减少',order: 3,view: 'text', type: 'string',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}