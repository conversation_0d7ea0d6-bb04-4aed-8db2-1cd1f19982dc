package org.jeecg.modules.book_words.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.book_words.entity.InzBookWords;
import org.jeecg.modules.book_words.service.IInzBookWordsService;
import org.jeecg.modules.words.entity.InzWords;
import org.jeecg.modules.words.service.IInzWordsService;
import org.jeecg.modules.inz_words_articles.service.IInzWordsArticlesService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

 /**
 * @Description: inz_book_words
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Api(tags="inz_book_words")
@RestController
@RequestMapping("/book_words/inzBookWords")
@Slf4j
public class InzBookWordsController extends JeecgController<InzBookWords, IInzBookWordsService> {
	@Autowired
	private IInzBookWordsService inzBookWordsService;
	
	@Autowired
	private IInzWordsArticlesService inzWordArticleService;
	
	@Autowired
	private IInzWordsService inzWordsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzBookWords
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "inz_book_words-分页列表查询")
	@ApiOperation(value="inz_book_words-分页列表查询", notes="inz_book_words-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzBookWords>> queryPageList(InzBookWords inzBookWords,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzBookWords> queryWrapper = QueryGenerator.initQueryWrapper(inzBookWords, req.getParameterMap());
		Page<InzBookWords> page = new Page<InzBookWords>(pageNo, pageSize);
		IPage<InzBookWords> pageList = inzBookWordsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzBookWords
	 * @return
	 */
	@AutoLog(value = "inz_book_words-添加")
	@ApiOperation(value="inz_book_words-添加", notes="inz_book_words-添加")
	@RequiresPermissions("book_words:inz_book_words:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzBookWords inzBookWords) {
		inzBookWordsService.save(inzBookWords);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzBookWords
	 * @return
	 */
	@AutoLog(value = "inz_book_words-编辑")
	@ApiOperation(value="inz_book_words-编辑", notes="inz_book_words-编辑")
	@RequiresPermissions("book_words:inz_book_words:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzBookWords inzBookWords) {
		inzBookWordsService.updateById(inzBookWords);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "inz_book_words-通过id删除")
	@ApiOperation(value="inz_book_words-通过id删除", notes="inz_book_words-通过id删除")
	@RequiresPermissions("book_words:inz_book_words:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzBookWordsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "inz_book_words-批量删除")
	@ApiOperation(value="inz_book_words-批量删除", notes="inz_book_words-批量删除")
	@RequiresPermissions("book_words:inz_book_words:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzBookWordsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "inz_book_words-通过id查询")
	@ApiOperation(value="inz_book_words-通过id查询", notes="inz_book_words-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzBookWords> queryById(@RequestParam(name="id",required=true) String id) {
		InzBookWords inzBookWords = inzBookWordsService.getById(id);
		if(inzBookWords==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzBookWords);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzBookWords
    */
    @RequiresPermissions("book_words:inz_book_words:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzBookWords inzBookWords) {
        return super.exportXls(request, inzBookWords, InzBookWords.class, "inz_book_words");
    }

    /**
     * 通过excel导入数据，并自动为导入的单词生成短文
    *
    * @param request
    * @param response
    * @return
    */
    /*@RequiresPermissions("book_words:inz_book_words:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                log.info("========== 开始导入Excel并生成短文 ==========");
                
                List<InzBookWords> list = ExcelImportUtil.importExcel(file.getInputStream(), InzBookWords.class, params);
                if (list == null || list.isEmpty()) {
                    log.error("Excel文件中未找到有效数据");
                    return Result.error("文件导入失败:数据为空");
                }
                
                log.info("成功从Excel中解析出 {} 条单词数据", list.size());
                
                // 保存导入的单词
                long start = System.currentTimeMillis();
                inzBookWordsService.saveBatch(list);
                log.info("保存单词完成，耗时: {}ms", System.currentTimeMillis() - start);
                
                // 按章节ID分组
                Map<String, List<InzBookWords>> chapterWordsMap = list.stream()
                    .filter(word -> word.getChapterId() != null && word.getWordId() != null)
                    .collect(Collectors.groupingBy(InzBookWords::getChapterId));
                
                log.info("导入的单词分布在 {} 个章节中", chapterWordsMap.size());
                
                // 为每个章节的单词生成短文
                for (Map.Entry<String, List<InzBookWords>> entry : chapterWordsMap.entrySet()) {
                    String chapterId = entry.getKey();
                    List<InzBookWords> chapterWords = entry.getValue();
                    
                    log.info("开始为章节 {} 生成短文，包含 {} 个单词", chapterId, chapterWords.size());
                    
                    // 尝试获取bookId
                    String bookId = null;
                    for (InzBookWords word : chapterWords) {
                        if (StringUtils.isNotBlank(word.getBookId())) {
                            bookId = word.getBookId();
                            break;
                        }
                    }
                    
                    if (StringUtils.isBlank(bookId)) {
                        log.warn("未能从章节 {} 的单词中找到有效的bookId，将使用null值", chapterId);
                    } else {
                        log.info("从章节 {} 中获取到bookId: {}", chapterId, bookId);
                    }
                    
                    try {
                        // 调用服务生成短文
                        log.info("开始调用服务为章节 {} 生成短文...", chapterId);
                        inzBookWordsService.generateArticlesForChapter(bookId, chapterId);
                        log.info("章节 {} 的短文生成任务已提交完成", chapterId);
                    } catch (Exception e) {
                        log.error("为章节 {} 生成短文时出错，继续处理其他章节", chapterId, e);
                    }
                }
                
                log.info("========== Excel导入和短文生成完成 ==========");
                return Result.ok("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                String msg = e.getMessage();
                log.error("Excel导入失败", e);
                if(msg!=null && msg.indexOf("Duplicate entry")>=0){
                    return Result.error("文件导入失败:有重复数据！");
                }else{
                    return Result.error("文件导入失败:" + e.getMessage());
                }
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }*/
    
    /**
     * 获取章节中已有短文包含的单词ID集合
     * 
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @return 已有短文中包含的单词ID集合
     */
    private Set<String> getExistingWordIdsInArticles(String bookId, String chapterId) {
        Set<String> existingWordIds = new HashSet<>();
        try {
            // 获取章节下所有短文
            List<String> wordIdLists = inzWordArticleService.getWordIdsInChapterArticles(bookId, chapterId);
            
            // 解析逗号分隔的单词ID列表，添加到集合中
            for (String wordIdList : wordIdLists) {
                if (wordIdList != null && !wordIdList.isEmpty()) {
                    String[] ids = wordIdList.split(",");
                    Collections.addAll(existingWordIds, ids);
                }
            }
        } catch (Exception e) {
            log.error("获取已有短文单词ID时出错", e);
        }
        return existingWordIds;
    }
    
    /**
     * 手动触发为章节单词生成短文（旧版本，保留以兼容现有调用）
     *
     * @param chapterId 章节ID
     * @return 生成结果
     */
    /*@GetMapping(value = "/generateArticlesForChapter")
    public Result<?> generateArticlesForChapter(@RequestParam(name="chapterId",required=true) String chapterId) {
        try {
            log.info("========== 旧版API: 手动触发生成短文 ==========");
            log.info("参数: chapterId={}", chapterId);
            
            if (StringUtils.isBlank(chapterId)) {
                log.error("生成短文参数无效: chapterId为空");
                return Result.error("章节ID不能为空");
            }
            
            // 获取章节下所有单词，尝试提取bookId
            LambdaQueryWrapper<InzBookWords> query = new LambdaQueryWrapper<>();
            query.eq(InzBookWords::getChapterId, chapterId);
            query.last("LIMIT 1");
            InzBookWords firstWord = inzBookWordsService.getOne(query);
            
            if (firstWord == null) {
                log.error("章节 {} 中未找到单词", chapterId);
                return Result.error("章节中未找到单词");
            }
            
            String bookId = firstWord.getBookId();
            if (StringUtils.isBlank(bookId)) {
                log.warn("未能从章节 {} 的单词中找到有效的bookId，将使用null值", chapterId);
            } else {
                log.info("从章节 {} 中获取到bookId: {}", chapterId, bookId);
            }
            
            log.info("开始调用服务生成短文...");
            inzBookWordsService.generateArticlesForChapter(bookId, chapterId);
            log.info("短文生成任务已提交完成");
            log.info("========== 短文生成流程已触发 ==========");
            
            return Result.OK("已触发短文生成流程，请查看日志了解详情");
        } catch (Exception e) {
            log.error("生成短文失败", e);
            return Result.error("生成短文失败：" + e.getMessage());
        }
    }*/

    /*@AutoLog(value = "词书单词-导入单词")
    @ApiOperation(value = "词书单词-导入单词", notes = "词书单词-导入单词")
    @PostMapping(value = "/importWords")
    public Result<?> importWords(@RequestParam(name = "file") MultipartFile file,
                                 @RequestParam(name = "bookId") String bookId,
                                 @RequestParam(name = "chapterId") String chapterId,
                                 HttpServletRequest request) {
        try {
            log.info("========== 开始导入单词并生成短文 ==========");
            log.info("参数: bookId={}, chapterId={}", bookId, chapterId);
            
            if (StringUtils.isBlank(bookId) || StringUtils.isBlank(chapterId)) {
                log.error("导入单词参数无效: bookId={}, chapterId={}", bookId, chapterId);
                return Result.error("词书ID和章节ID不能为空");
            }
            
            // 解析Excel
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            log.info("开始解析Excel文件...");
            List<InzBookWords> list = ExcelImportUtil.importExcel(file.getInputStream(), InzBookWords.class, params);
            
            if (list == null || list.isEmpty()) {
                log.warn("Excel文件中未找到有效数据");
                return Result.error("Excel文件中未找到有效数据");
            }
            
            log.info("成功从Excel中解析出 {} 条单词数据", list.size());
            
            // 设置bookId和chapterId
            for (InzBookWords inzBookWords : list) {
                inzBookWords.setBookId(bookId);
                inzBookWords.setChapterId(chapterId);
            }
            
            // 批量插入
            log.info("开始批量保存单词数据...");
            long startTime = System.currentTimeMillis();
            inzBookWordsService.saveBatch(list);
            log.info("批量保存单词完成，耗时: {}ms，成功保存 {} 条数据", System.currentTimeMillis() - startTime, list.size());
            
            // 导入单词后，为章节中的单词生成短文
            log.info("开始为导入的单词生成短文...");
            try {
                inzBookWordsService.generateArticlesForChapter(bookId, chapterId, file.getName());
                log.info("短文生成任务已提交完成");
            } catch (Exception e) {
                log.error("生成短文时出现异常，但单词已成功导入", e);
                return Result.OK("单词导入成功，但生成短文时出现错误：" + e.getMessage() + "。数据行数：" + list.size());
            }
            
            log.info("========== 单词导入和短文生成完成 ==========");
            return Result.OK("导入成功！数据行数：" + list.size());
        } catch (Exception e) {
            log.error("导入单词时发生异常", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }*/
    
    /**
     * 测试端点：手动触发为指定章节生成短文（新版本，同时使用bookId和chapterId）
     */
    /*@AutoLog(value = "词书单词-为章节生成短文")
    @ApiOperation(value = "词书单词-为章节生成短文", notes = "词书单词-为章节生成短文")
    @GetMapping(value = "/generateArticlesForChapterV2")
    public Result<?> manualGenerateArticles(@RequestParam(name = "bookId", required = true) String bookId,
                                           @RequestParam(name = "chapterId", required = true) String chapterId) {
        try {
            log.info("========== 手动触发生成短文 ==========");
            log.info("参数: bookId={}, chapterId={}", bookId, chapterId);
            
            if (StringUtils.isBlank(bookId) || StringUtils.isBlank(chapterId)) {
                log.error("生成短文参数无效: bookId={}, chapterId={}", bookId, chapterId);
                return Result.error("词书ID和章节ID不能为空");
            }
            
            log.info("开始调用服务生成短文...");
            inzBookWordsService.generateArticlesForChapter(bookId, chapterId);
            log.info("短文生成任务已提交完成");
            log.info("========== 短文生成流程已触发 ==========");
            
            return Result.OK("已触发短文生成流程，请查看日志了解详情");
        } catch (Exception e) {
            log.error("生成短文失败", e);
            return Result.error("生成短文失败：" + e.getMessage());
        }
    }*/
}
