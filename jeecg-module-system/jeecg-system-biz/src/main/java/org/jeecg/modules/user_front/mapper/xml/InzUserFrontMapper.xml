<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.user_front.mapper.InzUserFrontMapper">

    <!-- 推荐关系结果映射 -->
    <resultMap id="AgentReferralResultMap" type="org.jeecg.modules.user_front.vo.AgentReferralVO">
        <id column="id" property="id"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="role" property="role"/>
        <result column="status" property="status"/>
        <result column="golden_bean" property="goldenBean"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_name" property="parentName"/>
        <result column="parent_phone" property="parentPhone"/>
        <result column="level" property="level"/>
        <result column="create_time" property="createTime"/>
        <result column="last_use_at" property="lastUseAt"/>
        <result column="vip_time" property="vipTime"/>
        <result column="direct_referral_count" property="directReferralCount"/>
        <result column="indirect_referral_count" property="indirectReferralCount"/>
        <result column="total_referral_count" property="totalReferralCount"/>
        <result column="referral_path" property="referralPath"/>
    </resultMap>

    <!-- 查询代理商的直接推荐用户 -->
    <select id="selectDirectReferrals" resultMap="AgentReferralResultMap">
        SELECT
            u.id,
            u.real_name,
            u.phone,
            u.address,
            u.role,
            u.status,
            u.golden_bean,
            u.parent_id,
            p.real_name as parent_name,
            p.phone as parent_phone,
            1 as level,
            u.create_time,
            u.last_use_at,
            u.vip_time,
            (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) as direct_referral_count,
            (SELECT COUNT(*) FROM inz_user_front u2
             WHERE u2.parent_id IN (SELECT id FROM inz_user_front WHERE parent_id = u.id)) as indirect_referral_count,
            (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) +
            (SELECT COUNT(*) FROM inz_user_front u2
             WHERE u2.parent_id IN (SELECT id FROM inz_user_front WHERE parent_id = u.id)) as total_referral_count,
            CONCAT(p.real_name, ' → ', u.real_name) as referral_path
        FROM inz_user_front u
        LEFT JOIN inz_user_front p ON u.parent_id = p.id
        WHERE u.parent_id = #{agentId}
        ORDER BY u.create_time DESC
    </select>

    <!-- 查询代理商的间接推荐用户（二级推荐） - 应用代理商屏蔽规则 -->
    <select id="selectIndirectReferrals" resultMap="AgentReferralResultMap">
        SELECT
            u.id,
            u.real_name,
            u.phone,
            u.address,
            u.role,
            u.status,
            u.golden_bean,
            u.parent_id,
            p.real_name as parent_name,
            p.phone as parent_phone,
            2 as level,
            u.create_time,
            u.last_use_at,
            u.vip_time,
            (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) as direct_referral_count,
            0 as indirect_referral_count,
            (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) as total_referral_count,
            CONCAT(a.real_name, ' → ', p.real_name, ' → ', u.real_name) as referral_path
        FROM inz_user_front u
        LEFT JOIN inz_user_front p ON u.parent_id = p.id
        LEFT JOIN inz_user_front a ON p.parent_id = a.id
        WHERE u.parent_id IN (
            SELECT id FROM inz_user_front
            WHERE parent_id = #{agentId}
            -- 只查询直接推荐的非代理商用户的下级
            AND role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
        )
        -- 进一步过滤：如果二级用户是代理商，也要屏蔽
        AND u.role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
        ORDER BY u.create_time DESC
    </select>

    <!-- 查询代理商的所有推荐用户（直接+间接） -->
    <select id="selectAllReferrals" resultMap="AgentReferralResultMap">
        SELECT * FROM (
            -- 直接推荐用户
            SELECT
                u.id,
                u.real_name,
                u.phone,
                u.address,
                u.role,
                u.status,
                u.golden_bean,
                u.parent_id,
                p.real_name as parent_name,
                p.phone as parent_phone,
                1 as level,
                u.create_time,
                u.last_use_at,
                u.vip_time,
                -- 直接推荐数量（如果当前用户是代理商，则显示0，因为其下级被屏蔽）
                CASE
                    WHEN u.role IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province') THEN 0
                    ELSE (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id)
                END as direct_referral_count,
                -- 间接推荐数量（如果当前用户是代理商，则显示0）
                CASE
                    WHEN u.role IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province') THEN 0
                    ELSE (SELECT COUNT(*) FROM inz_user_front u2
                          WHERE u2.parent_id IN (
                              SELECT id FROM inz_user_front
                              WHERE parent_id = u.id
                              AND role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
                          )
                          AND u2.role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province'))
                END as indirect_referral_count,
                -- 总推荐数量
                CASE
                    WHEN u.role IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province') THEN 0
                    ELSE (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) +
                         (SELECT COUNT(*) FROM inz_user_front u2
                          WHERE u2.parent_id IN (
                              SELECT id FROM inz_user_front
                              WHERE parent_id = u.id
                              AND role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
                          )
                          AND u2.role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province'))
                END as total_referral_count,
                CONCAT(p.real_name, ' → ', u.real_name) as referral_path
            FROM inz_user_front u
            LEFT JOIN inz_user_front p ON u.parent_id = p.id
            WHERE u.parent_id = #{agentId}

            UNION ALL

            -- 间接推荐用户
            SELECT
                u.id,
                u.real_name,
                u.phone,
                u.address,
                u.role,
                u.status,
                u.golden_bean,
                u.parent_id,
                p.real_name as parent_name,
                p.phone as parent_phone,
                2 as level,
                u.create_time,
                u.last_use_at,
                u.vip_time,
                (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) as direct_referral_count,
                0 as indirect_referral_count,
                (SELECT COUNT(*) FROM inz_user_front WHERE parent_id = u.id) as total_referral_count,
                CONCAT(a.real_name, ' → ', p.real_name, ' → ', u.real_name) as referral_path
            FROM inz_user_front u
            LEFT JOIN inz_user_front p ON u.parent_id = p.id
            LEFT JOIN inz_user_front a ON p.parent_id = a.id
            WHERE u.parent_id IN (
                SELECT id FROM inz_user_front
                WHERE parent_id = #{agentId}
                -- 只查询直接推荐的非代理商用户的下级
                AND role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
            )
            -- 进一步过滤：如果二级用户是代理商，也要屏蔽
            AND u.role NOT IN ('chuang', 'channel', 'area_partner', 'city_partner', 'province')
        ) all_referrals
        ORDER BY level ASC, create_time DESC
    </select>

</mapper>