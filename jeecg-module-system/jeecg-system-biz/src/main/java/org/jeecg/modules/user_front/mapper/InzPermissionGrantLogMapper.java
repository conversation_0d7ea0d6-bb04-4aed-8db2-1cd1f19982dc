package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.user_front.entity.InzPermissionGrantLog;

import java.util.List;

/**
 * @Description: 权限开通记录Mapper
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Mapper
public interface InzPermissionGrantLogMapper extends BaseMapper<InzPermissionGrantLog> {

    /**
     * 查询用户的权限开通记录
     * @param userId 用户ID
     * @return 权限开通记录列表
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE user_id = #{userId} ORDER BY grant_time DESC")
    List<InzPermissionGrantLog> selectByUserId(@Param("userId") String userId);

    /**
     * 查询用户有效的权限开通记录
     * @param userId 用户ID
     * @return 有效的权限开通记录列表
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE user_id = #{userId} AND status = 'active' ORDER BY grant_time DESC")
    List<InzPermissionGrantLog> selectActiveByUserId(@Param("userId") String userId);

    /**
     * 查询用户最新的权限开通记录
     * @param userId 用户ID
     * @return 最新的权限开通记录
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE user_id = #{userId} AND status = 'active' ORDER BY grant_time DESC LIMIT 1")
    InzPermissionGrantLog selectLatestActiveByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID和教育阶段ID查询权限记录
     * @param userId 用户ID
     * @param educationId 教育阶段ID
     * @return 权限开通记录
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE user_id = #{userId} AND education_id = #{educationId} AND status = 'active' ORDER BY grant_time DESC LIMIT 1")
    InzPermissionGrantLog selectByUserAndEducation(@Param("userId") String userId, @Param("educationId") String educationId);

    /**
     * 更新权限记录状态
     * @param id 记录ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE inz_permission_grant_log SET status = #{status}, update_time = NOW(), update_by = #{updateBy} WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 批量更新用户的权限记录状态为已退单
     * @param userId 用户ID
     * @param updateBy 更新人
     * @return 更新数量
     */
    @Update("UPDATE inz_permission_grant_log SET status = 'refunded', update_time = NOW(), update_by = #{updateBy} WHERE user_id = #{userId} AND status = 'active'")
    int updateUserPermissionsToRefunded(@Param("userId") String userId, @Param("updateBy") String updateBy);

    /**
     * 统计操作者开通的权限数量
     * @param operatorId 操作者ID
     * @return 开通数量
     */
    @Select("SELECT COUNT(*) FROM inz_permission_grant_log WHERE operator_id = #{operatorId}")
    Integer countByOperatorId(@Param("operatorId") String operatorId);

    /**
     * 统计操作者开通权限消耗的总金币
     * @param operatorId 操作者ID
     * @return 总金币数
     */
    @Select("SELECT COALESCE(SUM(golden_bean_cost), 0) FROM inz_permission_grant_log WHERE operator_id = #{operatorId}")
    Integer sumGoldenBeanByOperatorId(@Param("operatorId") String operatorId);

    /**
     * 查询指定时间范围内的权限开通记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 权限开通记录列表
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE grant_time BETWEEN #{startTime} AND #{endTime} ORDER BY grant_time DESC")
    List<InzPermissionGrantLog> selectByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询用户在指定教育阶段的有效权限记录
     * @param userId 用户ID
     * @param educationId 教育阶段ID
     * @return 权限记录列表
     */
    @Select("SELECT * FROM inz_permission_grant_log WHERE user_id = #{userId} AND education_id = #{educationId} AND status = 'active'")
    List<InzPermissionGrantLog> selectActiveByUserAndEducation(@Param("userId") String userId, @Param("educationId") String educationId);
}
