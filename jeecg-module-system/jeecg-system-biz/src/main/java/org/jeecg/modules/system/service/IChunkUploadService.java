package org.jeecg.modules.system.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.UploadTask;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 分片上传服务接口
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IChunkUploadService {

    /**
     * 初始化分片上传任务
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param fileHash 文件MD5哈希值
     * @param chunkSize 分片大小(可选,默认5MB)
     * @param bizPath 业务路径(可选)
     * @param uploadType 上传类型(可选,默认local)
     * @return 上传任务信息
     */
    Result<UploadTask> initUploadTask(String fileName, Long fileSize, String fileHash, 
                                     Integer chunkSize, String bizPath, String uploadType);

    /**
     * 上传分片
     * @param taskId 任务ID
     * @param chunkIndex 分片索引
     * @param chunkFile 分片文件
     * @param chunkHash 分片哈希值(可选)
     * @return 上传结果
     */
    Result<String> uploadChunk(String taskId, Integer chunkIndex, MultipartFile chunkFile, String chunkHash);

    /**
     * 合并分片
     * @param taskId 任务ID
     * @return 合并结果，包含最终文件URL
     */
    Result<String> mergeChunks(String taskId);

    /**
     * 检查分片上传状态
     * @param taskId 任务ID
     * @return 上传状态信息
     */
    Result<Map<String, Object>> checkUploadStatus(String taskId);

    /**
     * 获取已上传的分片列表
     * @param taskId 任务ID
     * @return 已上传分片索引列表
     */
    Result<List<Integer>> getUploadedChunks(String taskId);

    /**
     * 取消上传任务
     * @param taskId 任务ID
     * @return 取消结果
     */
    Result<String> cancelUpload(String taskId);

    /**
     * 重试失败的分片
     * @param taskId 任务ID
     * @return 重试结果
     */
    Result<String> retryFailedChunks(String taskId);

    /**
     * 秒传检查 - 检查文件是否已存在
     * @param fileHash 文件哈希值
     * @param fileSize 文件大小
     * @return 秒传结果，如果文件已存在则返回文件URL
     */
    Result<String> checkInstantUpload(String fileHash, Long fileSize);

    /**
     * 获取用户的上传任务列表
     * @param status 状态过滤(可选)
     * @param limit 限制数量(可选)
     * @return 任务列表
     */
    Result<List<UploadTask>> getUserUploadTasks(String status, Integer limit);

    /**
     * 删除上传任务及相关文件
     * @param taskId 任务ID
     * @return 删除结果
     */
    Result<String> deleteUploadTask(String taskId);

    /**
     * 清理过期任务
     * @return 清理结果
     */
    Result<String> cleanExpiredTasks();

    /**
     * 获取上传进度
     * @param taskId 任务ID
     * @return 进度信息
     */
    Result<Map<String, Object>> getUploadProgress(String taskId);

    /**
     * 暂停上传任务
     * @param taskId 任务ID
     * @return 暂停结果
     */
    Result<String> pauseUpload(String taskId);

    /**
     * 恢复上传任务
     * @param taskId 任务ID
     * @return 恢复结果
     */
    Result<String> resumeUpload(String taskId);

    /**
     * 验证分片完整性
     * @param taskId 任务ID
     * @return 验证结果
     */
    Result<Map<String, Object>> validateChunks(String taskId);

    /**
     * 获取上传统计信息
     * @param userId 用户ID(可选)
     * @return 统计信息
     */
    Result<Map<String, Object>> getUploadStatistics(String userId);
}
