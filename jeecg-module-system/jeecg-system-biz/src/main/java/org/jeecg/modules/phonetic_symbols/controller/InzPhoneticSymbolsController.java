package org.jeecg.modules.phonetic_symbols.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.phonetic_symbols.entity.InzPhoneticSymbols;
import org.jeecg.modules.phonetic_symbols.service.IInzPhoneticSymbolsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 音标管理
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Api(tags="音标管理")
@RestController
@RequestMapping("/phonetic_symbols/inzPhoneticSymbols")
@Slf4j
public class InzPhoneticSymbolsController extends JeecgController<InzPhoneticSymbols, IInzPhoneticSymbolsService> {
	@Autowired
	private IInzPhoneticSymbolsService inzPhoneticSymbolsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzPhoneticSymbols
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "音标管理-分页列表查询")
	@ApiOperation(value="音标管理-分页列表查询", notes="音标管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzPhoneticSymbols>> queryPageList(InzPhoneticSymbols inzPhoneticSymbols,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzPhoneticSymbols> queryWrapper = QueryGenerator.initQueryWrapper(inzPhoneticSymbols, req.getParameterMap());
		Page<InzPhoneticSymbols> page = new Page<InzPhoneticSymbols>(pageNo, pageSize);
		IPage<InzPhoneticSymbols> pageList = inzPhoneticSymbolsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzPhoneticSymbols
	 * @return
	 */
	@AutoLog(value = "音标管理-添加")
	@ApiOperation(value="音标管理-添加", notes="音标管理-添加")
	@RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzPhoneticSymbols inzPhoneticSymbols) {
		inzPhoneticSymbolsService.save(inzPhoneticSymbols);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzPhoneticSymbols
	 * @return
	 */
	@AutoLog(value = "音标管理-编辑")
	@ApiOperation(value="音标管理-编辑", notes="音标管理-编辑")
	@RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzPhoneticSymbols inzPhoneticSymbols) {
		inzPhoneticSymbolsService.updateById(inzPhoneticSymbols);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "音标管理-通过id删除")
	@ApiOperation(value="音标管理-通过id删除", notes="音标管理-通过id删除")
	@RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzPhoneticSymbolsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "音标管理-批量删除")
	@ApiOperation(value="音标管理-批量删除", notes="音标管理-批量删除")
	@RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzPhoneticSymbolsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "音标管理-通过id查询")
	@ApiOperation(value="音标管理-通过id查询", notes="音标管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzPhoneticSymbols> queryById(@RequestParam(name="id",required=true) String id) {
		InzPhoneticSymbols inzPhoneticSymbols = inzPhoneticSymbolsService.getById(id);
		if(inzPhoneticSymbols==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzPhoneticSymbols);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzPhoneticSymbols
    */
    @RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzPhoneticSymbols inzPhoneticSymbols) {
        return super.exportXls(request, inzPhoneticSymbols, InzPhoneticSymbols.class, "音标管理");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("phonetic_symbols:inz_phonetic_symbols:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzPhoneticSymbols.class);
    }

}
