package org.jeecg.modules.user_books.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户选择图书管理
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
@Data
@TableName("inz_user_books")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_books对象", description="用户选择图书管理")
public class InzUserBooks implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**图书id*/
	@Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id")
    private String wordBookId;
	/**状态 1使用 2停用*/
	@Excel(name = "状态 1使用 2停用", width = 15)
    @ApiModelProperty(value = "状态 1使用 2停用")
    private Integer status;

    /**过期时间 */
    @Excel(name = "过期开始时间", width = 15)
    @ApiModelProperty(value = "过期开始时间")
    private String expirationStartData;
    /**过期时间 */
    @Excel(name = "过期结束时间", width = 15)
    @ApiModelProperty(value = "过期结束时间")
    private String expirationEndData;
}
