package org.jeecg.modules.inz_learning_videos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Description: 根据名称添加视频DTO
 * @Author: Alex (工程师)
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "VideoAddByNameDTO", description = "根据名称添加视频数据传输对象")
public class VideoAddByNameDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模块ID", required = true)
    @NotBlank(message = "模块ID不能为空")
    private String moduleId;

    @ApiModelProperty(value = "一级分类ID", required = true)
    @NotBlank(message = "一级分类ID不能为空")
    private String firstLevelCategoryId;

    @ApiModelProperty(value = "分类ID（二级分类）", required = true)
    @NotBlank(message = "分类ID不能为空")
    private String categoryId;

    @ApiModelProperty(value = "视频标题", required = true)
    @NotBlank(message = "视频标题不能为空")
    @Size(max = 200, message = "视频标题长度不能超过200个字符")
    private String videoTitle;

    @ApiModelProperty(value = "视频描述")
    @Size(max = 1000, message = "视频描述长度不能超过1000个字符")
    private String description;

    @ApiModelProperty(value = "视频文件URL")
    private String videoUrl;

    @ApiModelProperty(value = "封面图片URL")
    private String coverImage;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "状态 0-禁用 1-启用", notes = "默认为1启用")
    private Integer status = 1;
}
