package org.jeecg.modules.inz_words_articles.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.system.base.entity.ConstructionEntity;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.inz_words_articles.entity.InzWordsArticles;
import org.jeecg.modules.inz_words_articles.mapper.InzWordsArticlesMapper;
import org.jeecg.modules.inz_words_articles.service.IInzWordsArticlesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 短文表
 * @Author: jeecg-boot
 * @Date: 2025-07-06
 * @Version: V1.0
 */
@Service
public class InzWordsArticlesServiceImpl extends ServiceImpl<InzWordsArticlesMapper, InzWordsArticles> implements IInzWordsArticlesService {

    private static final Logger logger = LoggerFactory.getLogger(InzWordsArticlesServiceImpl.class);

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<String> getWordIdsInChapterArticles(String bookId, String chapterId) {
        if (StringUtils.isBlank(chapterId)) {
            logger.warn("获取章节短文单词ID时，章节ID为空");
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<InzWordsArticles> query = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(bookId)) {
            query.eq(InzWordsArticles::getBookId, bookId);
        }
        query.eq(InzWordsArticles::getChapterId, chapterId);
        query.select(InzWordsArticles::getWordIds);

        // 查询所有符合条件的记录
        List<InzWordsArticles> articles = this.list(query);
        logger.debug("查询到章节 {} 的短文数量: {}", chapterId, articles.size());

        // 提取wordIds字段
        List<String> wordIdGroups = articles.stream()
                .map(InzWordsArticles::getWordIds)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        logger.debug("章节 {} 中短文包含的单词组数量: {}", chapterId, wordIdGroups.size());
        return wordIdGroups;
    }

    @Override
    public int generateAndSaveArticles(List<String> wordIds, List<String> words, String bookId, String chapterId, int groupIndex, int count, String fileName) {
        logger.info("========== 开始生成短文 ==========");
        logger.info("参数: bookId={}, chapterId={}, groupIndex={}, count={}", bookId, chapterId, groupIndex, count);

        if (CollectionUtils.isEmpty(wordIds) || CollectionUtils.isEmpty(words) ||
                StringUtils.isBlank(chapterId) || count <= 0) {
            logger.error("生成短文参数无效: wordIds={}, words={}, chapterId={}, count={}",
                    wordIds != null ? wordIds.size() : "null",
                    words != null ? words.size() : "null",
                    chapterId, count);
            return 0;
        }

        if (wordIds.size() != words.size()) {
            logger.error("单词ID列表和单词列表长度不一致: wordIds.size={}, words.size={}",
                    wordIds.size(), words.size());
            return 0;
        }

        // 详细记录输入参数
        logger.info("单词ID列表 ({}): {}", wordIds.size(), wordIds);
        logger.info("单词列表 ({}): {}", words.size(), words);

        // 过滤掉空单词
        List<String> validWords = words.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 对应更新wordIds
        List<String> validWordIds = new ArrayList<>();
        for (int i = 0; i < words.size(); i++) {
            if (StringUtils.isNotBlank(words.get(i))) {
                validWordIds.add(wordIds.get(i));
            }
        }

        if (validWords.isEmpty()) {
            logger.error("过滤后没有有效的单词，无法生成短文");
            return 0;
        }

        logger.info("过滤后的有效单词数量: {}", validWords.size());
        logger.info("有效单词: {}", validWords);
        logger.info("有效单词ID: {}", validWordIds);
        logger.info("开始为 {} 个单词调用第三方API生成短文", validWords.size());

        try {
            // 获取第三方API Token
            String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
            logger.debug("获取到第三方API Token: {}", thirdToken != null ? "成功" : "失败");

            // 单词ID和单词列表转为逗号分隔的字符串
            String wordIdsStr = String.join(",", validWordIds);
            String wordsStr = String.join(",", validWords);

            int savedCount = 0;
            int maxAttempts = count * 2; // 设置最大尝试次数，避免无限循环
            int attempts = 0;

            // 循环调用API直到生成足够数量的短文或达到最大尝试次数
            while (savedCount < count && attempts < maxAttempts) {
                attempts++;
                logger.info("尝试生成短文 #{}/{} (已保存: {}/{})", attempts, maxAttempts, savedCount, count);

                try {
                    logger.info("调用第三方API生成短文...");
                    List<ConstructionEntity> constructionEntities =
                            ThirdRequestUtils.analyzeConstruction(validWords, thirdToken, redisUtil, fileName);

                    if (CollectionUtils.isEmpty(constructionEntities)) {
                        logger.error("API返回空结果，尝试重新调用");
                        continue;
                    }

                    logger.info("API返回 {} 篇短文", constructionEntities.size());

                    // 处理API返回的短文
                    for (int i = 0; i < constructionEntities.size() && savedCount < count; i++) {
                        ConstructionEntity entity = constructionEntities.get(i);

                        // 检查文章内容是否为空
                        if (entity == null || StringUtils.isBlank(entity.getArticle())) {
                            logger.warn("短文内容为空，跳过");
                            continue;
                        }
                        logger.debug("短文内容长度: {} 字符", entity.getArticle().length());

                        InzWordsArticles article = new InzWordsArticles();
                        article.setChapterId(chapterId);
                        if (StringUtils.isNotBlank(bookId)) {
                            article.setBookId(bookId);
                        }
                        article.setWordIds(wordIdsStr);
                        article.setWords(wordsStr);
                        article.setContent(entity.getArticle());
                        article.setChineseTranslation(entity.getChMeaning());
                        article.setTitle("单词短文 #" + (savedCount + 1));
                        article.setGroupIndex(groupIndex);
                        article.setQuestionsJson(JSONObject.toJSONString(entity.getQuestionList()));
                        if (entity.getQuestionList() != null) {
                            logger.info("问题列表: {}", entity.getQuestionList());
                            logger.info("问题列表JSON: {}", JSONObject.toJSONString(entity.getQuestionList()));
                            if (article.getQuestionsJson() != null) {
                                logger.info("问题列表保存成功JSON: {}", article.getQuestionsJson());
                            }
                        }

                        logger.debug("保存短文 #{} 到数据库...", savedCount + 1);
                        if (save(article)) {
                            savedCount++;
                            logger.debug("短文 #{} 保存成功，ID: {}", savedCount, article.getId());
                        } else {
                            logger.warn("短文保存失败");
                        }
                    }
                    // 如果API返回了短文但还没有达到目标数量，等待一小段时间再次调用
                    if (savedCount < count) {
                        try {
                            Thread.sleep(500); // 等待500毫秒
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }
                } catch (Exception e) {
                    logger.error("调用API生成短文时出现异常", e);
                    // 出现异常时等待更长时间再重试
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            logger.info("========== 短文生成完成 ==========");
            logger.info("成功生成并保存 {} 篇短文 (目标: {})", savedCount, count);
            return savedCount;
        } catch (Exception e) {
            logger.error("生成短文时出现异常", e);
            return 0;
        }
    }
}
