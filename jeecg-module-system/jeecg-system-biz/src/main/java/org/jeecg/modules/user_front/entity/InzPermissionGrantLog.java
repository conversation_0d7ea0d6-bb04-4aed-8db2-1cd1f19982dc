package org.jeecg.modules.user_front.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 权限开通记录表
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@TableName("inz_permission_grant_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_permission_grant_log对象", description="权限开通记录表")
public class InzPermissionGrantLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**操作者ID（代理商ID）*/
    @Excel(name = "操作者ID", width = 15)
    @ApiModelProperty(value = "操作者ID（代理商ID）")
    private String operatorId;

    /**教育阶段ID*/
    @Excel(name = "教育阶段ID", width = 15)
    @ApiModelProperty(value = "教育阶段ID")
    private String educationId;

    /**教育阶段名称*/
    @Excel(name = "教育阶段名称", width = 20)
    @ApiModelProperty(value = "教育阶段名称")
    private String educationName;

    /**开通类型*/
    @Excel(name = "开通类型", width = 15)
    @ApiModelProperty(value = "开通类型（annual:全年权限, trial:试用权限）")
    private String grantType;

    /**消耗金币数量*/
    @Excel(name = "消耗金币数量", width = 15)
    @ApiModelProperty(value = "消耗金币数量")
    private Integer goldenBeanCost;

    /**开通的单词书数量*/
    @Excel(name = "开通的单词书数量", width = 15)
    @ApiModelProperty(value = "开通的单词书数量")
    private Integer wordBookCount;

    /**开通时间*/
    @Excel(name = "开通时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开通时间")
    private Date grantTime;

    /**过期时间*/
    @Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expirationTime;

    /**状态*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态（active:有效, expired:已过期, refunded:已退单）")
    private String status;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
