package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 分片上传任务实体
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@TableName("upload_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="UploadTask对象", description="分片上传任务")
public class UploadTask implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**任务ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "任务ID")
    private String id;

    /**文件名*/
    @Excel(name = "文件名", width = 15)
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**文件总大小(字节)*/
    @Excel(name = "文件大小", width = 15)
    @ApiModelProperty(value = "文件总大小(字节)")
    private Long fileSize;

    /**文件MD5哈希值*/
    @ApiModelProperty(value = "文件MD5哈希值")
    private String fileHash;

    /**分片大小(字节,默认5MB)*/
    @ApiModelProperty(value = "分片大小(字节)")
    private Integer chunkSize;

    /**总分片数*/
    @Excel(name = "总分片数", width = 15)
    @ApiModelProperty(value = "总分片数")
    private Integer totalChunks;

    /**已上传分片数*/
    @Excel(name = "已上传分片数", width = 15)
    @ApiModelProperty(value = "已上传分片数")
    private Integer uploadedChunks;

    /**状态:UPLOADING,COMPLETED,FAILED,CANCELLED*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;

    /**上传类型:local,minio,alioss*/
    @ApiModelProperty(value = "上传类型")
    private String uploadType;

    /**业务路径*/
    @ApiModelProperty(value = "业务路径")
    private String bizPath;

    /**最终文件URL*/
    @ApiModelProperty(value = "最终文件URL")
    private String finalUrl;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    /**过期时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expiredTime;

    // 状态常量
    public static final String STATUS_UPLOADING = "UPLOADING";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_CANCELLED = "CANCELLED";

    // 默认分片大小 5MB
    public static final int DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024;

    /**
     * 计算上传进度百分比
     * @return 进度百分比 (0-100)
     */
    public double getProgress() {
        if (totalChunks == null || totalChunks == 0) {
            return 0.0;
        }
        return (double) uploadedChunks / totalChunks * 100;
    }

    /**
     * 检查是否上传完成
     * @return true if completed
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }

    /**
     * 检查是否上传失败
     * @return true if failed
     */
    public boolean isFailed() {
        return STATUS_FAILED.equals(status);
    }

    /**
     * 检查是否正在上传
     * @return true if uploading
     */
    public boolean isUploading() {
        return STATUS_UPLOADING.equals(status);
    }

    /**
     * 检查是否已取消
     * @return true if cancelled
     */
    public boolean isCancelled() {
        return STATUS_CANCELLED.equals(status);
    }

    /**
     * 格式化文件大小显示
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", (double) size, units[unitIndex]);
    }
}
