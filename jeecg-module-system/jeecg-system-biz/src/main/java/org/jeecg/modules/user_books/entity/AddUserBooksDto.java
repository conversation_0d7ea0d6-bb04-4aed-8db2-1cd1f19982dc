package org.jeecg.modules.user_books.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 用户选择图书管理
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_books对象", description="用户选择图书管理")
public class AddUserBooksDto implements Serializable {
    @ApiModelProperty(value = "图书分类ID列表", required = true, example = "1878987782752366594,1878988993991864322")
    private String categoryIds;

    @ApiModelProperty(value = "图书ID列表", required = true, example = "0fcb4b1a146e3a0344da3509cc317c59,09b25bc620de3c26eb406f4b00ee34db")
    private String wordBookIds;

    @ApiModelProperty(value = "所需金豆总数", example = "240")
    private Integer totalGoldenBean;

    @ApiModelProperty(value = "有效期时间范围", required = true)
    private String[] timeRange;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userIds;
}
