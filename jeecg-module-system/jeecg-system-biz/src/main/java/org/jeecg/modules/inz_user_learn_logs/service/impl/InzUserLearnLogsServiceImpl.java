package org.jeecg.modules.inz_user_learn_logs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.inz_user_learn_logs.entity.InzUserLearnLogs;
import org.jeecg.modules.inz_user_learn_logs.mapper.InzUserLearnLogsMapper;
import org.jeecg.modules.inz_user_learn_logs.service.IInzUserLearnLogsService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 学习记录
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Slf4j
@Service
public class InzUserLearnLogsServiceImpl extends ServiceImpl<InzUserLearnLogsMapper, InzUserLearnLogs> implements IInzUserLearnLogsService {

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Override
    public Result<IPage<InzUserLearnLogs>> queryPageList(HttpServletRequest req, QueryWrapper<InzUserLearnLogs> queryWrapper, Integer pageSize, Integer pageNo) {
        Result<IPage<InzUserLearnLogs>> result = new Result<IPage<InzUserLearnLogs>>();

        try {
            // 1. 根据手机号过滤用户（如果提供了phone参数）
            String phone = req.getParameter("phone");
            if (phone == null && req.getAttribute("phone") != null) {
                phone = req.getAttribute("phone").toString();
            }
            if (oConvertUtils.isNotEmpty(phone)) {
                LambdaQueryWrapper<InzUserFront> userQueryWrapper = new LambdaQueryWrapper<>();
                userQueryWrapper.eq(InzUserFront::getPhone, phone);
                InzUserFront user = inzUserFrontService.getOne(userQueryWrapper);

                if (user == null) {
                    return Result.error("用户不存在");
                }

                // 添加用户过滤条件
                queryWrapper.eq("create_by", user.getId());
            }

            // 2. 根据用户ID过滤（如果提供了userId参数）
            String userId = req.getParameter("userId");
            if (userId == null && req.getAttribute("userId") != null) {
                userId = req.getAttribute("userId").toString();
            }
            if (oConvertUtils.isNotEmpty(userId)) {
                queryWrapper.eq("create_by", userId);
            }

            // 3. 根据用户ID列表过滤（如果提供了userIds参数）
            String userIds = req.getParameter("userIds");
            if (userIds == null && req.getAttribute("userIds") != null) {
                userIds = req.getAttribute("userIds").toString();
            }
            if (oConvertUtils.isNotEmpty(userIds)) {
                queryWrapper.in("create_by", Arrays.asList(userIds.split(",")));
                pageSize = userIds.split(",").length * 10; // 调整页面大小
            }

            // 4. 根据学习状态过滤（如果提供了status参数）
            String status = req.getParameter("status");
            if (oConvertUtils.isNotEmpty(status)) {
                queryWrapper.eq("status", status);
            }

            // 5. 根据学习类型过滤（如果提供了learnType参数）
            String learnType = req.getParameter("learnType");
            if (oConvertUtils.isNotEmpty(learnType)) {
                queryWrapper.eq("learn_type", learnType);
            }

            // 6. 根据教育分类过滤（如果提供了educationCategory参数）
            String educationCategory = req.getParameter("educationCategory");
            if (oConvertUtils.isNotEmpty(educationCategory)) {
                queryWrapper.eq("education_category", educationCategory);
            }

            // 7. 权限控制：根据当前登录用户的角色和权限决定可见数据范围
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser != null) {
                // 检查用户角色
                List<String> roles = loginUser.getRoles();

                if (roles != null && !roles.isEmpty()) {
                    boolean isAdmin = roles.contains("admin") || roles.contains("super_admin");
                    boolean isTeacher = roles.contains("teacher") || roles.contains("coach");
                    boolean isStudent = roles.contains("student") || roles.contains("user");

                    if (isAdmin) {
                        // 管理员可以看到所有学习记录
                        log.info("管理员用户 {} 查询所有学习记录", loginUser.getUsername());
                    } else if (isTeacher) {
                        // 教师只能看到自己班级学生的学习记录
                        // 这里可以根据实际业务逻辑添加班级关联查询
                        log.info("教师用户 {} 查询班级学生学习记录", loginUser.getUsername());
                        // TODO: 添加班级学生过滤逻辑
                    } else if (isStudent) {
                        // 学生只能看到自己的学习记录
                        queryWrapper.eq("create_by", loginUser.getId());
                        log.info("学生用户 {} 查询自己的学习记录", loginUser.getUsername());
                    } else {
                        // 其他角色只能看到自己的学习记录
                        queryWrapper.eq("create_by", loginUser.getId());
                        log.info("普通用户 {} 查询自己的学习记录", loginUser.getUsername());
                    }
                } else {
                    // 没有角色的用户只能看到自己的学习记录
                    queryWrapper.eq("create_by", loginUser.getId());
                    log.info("无角色用户 {} 查询自己的学习记录", loginUser.getUsername());
                }
            }

            // 8. 默认排序
            queryWrapper.orderByDesc("create_time");

            // 9. 分页查询
            Page<InzUserLearnLogs> page = new Page<InzUserLearnLogs>(pageNo, pageSize);
            IPage<InzUserLearnLogs> pageList = this.page(page, queryWrapper);

            result.setResult(pageList);
            result.setSuccess(true);
            result.setMessage("查询成功");

            log.info("学习记录查询完成 - 用户: {}, 总记录数: {}, 当前页: {}/{}",
                    loginUser != null ? loginUser.getUsername() : "匿名",
                    pageList.getTotal(), pageNo, pageList.getPages());

        } catch (Exception e) {
            log.error("查询学习记录失败", e);
            result.setSuccess(false);
            result.setMessage("查询失败: " + e.getMessage());
        }

        return result;
    }
}
