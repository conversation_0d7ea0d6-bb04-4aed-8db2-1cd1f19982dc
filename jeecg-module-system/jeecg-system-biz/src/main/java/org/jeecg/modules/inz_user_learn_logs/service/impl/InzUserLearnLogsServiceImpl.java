package org.jeecg.modules.inz_user_learn_logs.service.impl;

import org.jeecg.modules.inz_user_learn_logs.entity.InzUserLearnLogs;
import org.jeecg.modules.inz_user_learn_logs.mapper.InzUserLearnLogsMapper;
import org.jeecg.modules.inz_user_learn_logs.service.IInzUserLearnLogsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 学习记录
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Service
public class InzUserLearnLogsServiceImpl extends ServiceImpl<InzUserLearnLogsMapper, InzUserLearnLogs> implements IInzUserLearnLogsService {

}
