package org.jeecg.modules.inz_learning_categorys.service;

import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 继续深造-分类
 * @Author: jeecg-boot
 * @Date:   2025-08-03
 * @Version: V1.0
 */
public interface IInzLearningCategorysService extends IService<InzLearningCategorys> {

    /**
     * 根据父分类ID获取子分类列表
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<InzLearningCategorys> getSubCategories(String parentId);

    /**
     * 级联删除分类及其所有子分类
     * @param categoryId 分类ID
     * @return 删除结果
     */
    boolean cascadeDeleteCategory(String categoryId);

    /**
     * 检查分类是否可以删除（没有关联的视频和子分类）
     * @param categoryId 分类ID
     * @return 检查结果
     */
    boolean canDeleteCategory(String categoryId);

}
