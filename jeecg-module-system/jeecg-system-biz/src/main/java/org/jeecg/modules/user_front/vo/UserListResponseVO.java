package org.jeecg.modules.user_front.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.user_front.entity.InzUserFront;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 用户列表响应VO（包含用户信息和金币统计）
 * @Author: Alex
 * @Date: 2025-08-02
 * @Version: V1.0
 */
@Data
@ApiModel(value = "UserListResponseVO", description = "用户列表响应信息")
public class UserListResponseVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户列表数据")
    private List<InzUserFront> records;
    
    @ApiModelProperty(value = "总记录数")
    private Long total;
    
    @ApiModelProperty(value = "每页大小")
    private Long size;
    
    @ApiModelProperty(value = "当前页")
    private Long current;
    
    @ApiModelProperty(value = "当前登录用户信息")
    private CurrentUserInfoVO userInfo;
    
    @ApiModelProperty(value = "金币统计信息（仅代理商）")
    private GoldenBeanStatsVO goldenBeanStats;

    /**
     * 从IPage对象创建UserListResponseVO
     */
    public static UserListResponseVO fromIPage(IPage<InzUserFront> page) {
        UserListResponseVO response = new UserListResponseVO();
        response.setRecords(page.getRecords());
        response.setTotal(page.getTotal());
        response.setSize(page.getSize());
        response.setCurrent(page.getCurrent());
        return response;
    }
}
