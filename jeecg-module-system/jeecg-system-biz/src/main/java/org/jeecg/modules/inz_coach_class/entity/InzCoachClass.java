package org.jeecg.modules.inz_coach_class.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 班级表
 * @Author: jeecg-boot
 * @Date:   2025-08-02
 * @Version: V1.0
 */
@Data
@TableName("inz_coach_class")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_coach_class对象", description="班级表")
public class InzCoachClass implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private java.lang.String id;
	/**班级名称*/
	@Excel(name = "班级名称", width = 15)
    @ApiModelProperty(value = "班级名称")
    private java.lang.String className;
	/**班级编码*/
	@Excel(name = "班级编码", width = 15)
    @ApiModelProperty(value = "班级编码")
    private java.lang.String classCode;
	/**教练ID（创建者）*/
	@Excel(name = "教练ID（创建者）", width = 15)
    @ApiModelProperty(value = "教练ID（创建者）")
    private java.lang.String coachId;
	/**教练姓名*/
	@Excel(name = "教练姓名", width = 15)
    @ApiModelProperty(value = "教练姓名")
    private java.lang.String coachName;
	/**班级描述*/
	@Excel(name = "班级描述", width = 15)
    @ApiModelProperty(value = "班级描述")
    private java.lang.String description;
	/**当前学生数量*/
	@Excel(name = "当前学生数量", width = 15)
    @ApiModelProperty(value = "当前学生数量")
    private java.lang.Integer currentStudents;
	/**状态 0-停用 1-启用*/
	@Excel(name = "状态 0-停用 1-启用", width = 15)
    @ApiModelProperty(value = "状态 0-停用 1-启用")
    private java.lang.Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
}
