package org.jeecg.modules.inz_coach_class.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeecg.dingtalk.api.user.vo.UserRole;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.inz_coach_class.entity.InzCoachClass;
import org.jeecg.modules.inz_coach_class.service.IInzCoachClassService;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.inz_coach_class_student.entity.InzCoachClassStudent;
import org.jeecg.modules.inz_coach_class_student.service.IInzCoachClassStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 班级表
 * @Author: jeecg-boot
 * @Date: 2025-08-02
 * @Version: V1.0
 */
@Api(tags = "班级表")
@RestController
@RequestMapping("/inz_coach_class/inzCoachClass")
@Slf4j
public class InzCoachClassController extends JeecgController<InzCoachClass, IInzCoachClassService> {
    @Autowired
    private IInzCoachClassService inzCoachClassService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IInzUserFrontService inzUserFrontService;
    @Autowired
    private IInzCoachClassStudentService inzCoachClassStudentService;

    /**
     * 分页列表查询
     *
     * @param inzCoachClass
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "班级表-分页列表查询")
    @ApiOperation(value = "班级表-分页列表查询", notes = "班级表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzCoachClass>> queryPageList(InzCoachClass inzCoachClass,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        QueryWrapper<InzCoachClass> queryWrapper = QueryGenerator.initQueryWrapper(inzCoachClass, req.getParameterMap());
        Page<InzCoachClass> page = new Page<InzCoachClass>(pageNo, pageSize);
        IPage<InzCoachClass> pageList = inzCoachClassService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "获取教练列表", notes = "获取教练列表")
    @GetMapping(value = "/getCoachList")
    public Result<List<SysUser>> getCoachList() {
        LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
        roleQuery.eq(SysRole::getRoleCode, "coach");
        SysRole coachRole = sysRoleService.getOne(roleQuery);
        LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
        userRoleQuery.eq(SysUserRole::getRoleId, coachRole.getId());
        List<SysUserRole> userRoleList = sysUserRoleService.list(userRoleQuery);
        List<SysUser> coachList = userRoleList.stream().map(SysUserRole::getUserId).map(sysUserService::getById).collect(Collectors.toList());
        return Result.OK(coachList);
    }

    @ApiOperation(value = "获取班级列表", notes = "获取班级列表")
    @GetMapping(value = "/getClassList")
    public Result<List<InzCoachClass>> getClassList() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaQueryWrapper<InzCoachClass> query = new LambdaQueryWrapper<>();
        query.eq(InzCoachClass::getCoachId, loginUser.getId());
        List<InzCoachClass> classList = inzCoachClassService.list(query);
        return Result.OK(classList);
    }

    /**
     * 根据用户姓名+电话，把服务的用户加入自己创建的班级
     */
    @AutoLog(value = "班级表-添加用户到班级")
    @ApiOperation(value = "用户添加进班级", notes = "把服务的用户加入自己创建的班级")
    @PostMapping(value = "/addUserToClass")
    public Result<String> addUserToClass(@RequestBody AddUserToClassRequest request) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

            // 1. 验证操作者权限
            String userRoles = loginUser.getRoleCode();
            if (!userRoles.equals("coach") && !userRoles.equals("admin")) {
                return Result.error("您没有权限，无法添加用户到班级");
            }

            // 2. 验证班级是否存在且属于当前教练
            InzCoachClass coachClass = inzCoachClassService.getById(request.getClassId());
            if (coachClass == null) {
                return Result.error("班级不存在");
            }

            if (!coachClass.getCoachId().equals(loginUser.getId())) {
                return Result.error("您只能为自己创建的班级添加学生");
            }

            InzUserFront targetUser = inzUserFrontService.getById(request.getUserId());

            if (targetUser == null) {
                return Result.error("未找到匹配的用户，请检查用户ID是否正确");
            }

            // 4. 检查用户是否已在该班级中
            QueryWrapper<InzCoachClassStudent> classStudentQuery = new QueryWrapper<>();
            classStudentQuery.eq("class_id", request.getClassId());
            classStudentQuery.eq("student_id", targetUser.getId());
            long existCount = inzCoachClassStudentService.count(classStudentQuery);

            if (existCount > 0) {
                return Result.error("用户 " + targetUser.getRealName() + " 已在该班级中");
            }

            // 5. 创建班级学生记录
            InzCoachClassStudent classStudent = new InzCoachClassStudent();
            classStudent.setClassId(request.getClassId());
            classStudent.setStudentId(targetUser.getId());
            classStudent.setStudentName(targetUser.getRealName());
            classStudent.setStudentPhone(targetUser.getPhone());
            classStudent.setJoinTime(new Date());
            classStudent.setCreateBy(loginUser.getUsername());
            classStudent.setCreateTime(new Date());

            inzCoachClassStudentService.save(classStudent);

            // 6. 更新班级学生数量
            coachClass.setCurrentStudents(coachClass.getCurrentStudents() + 1);
            inzCoachClassService.updateById(coachClass);

            log.info("用户添加到班级成功 - 用户: {} ({}), 班级: {}, 操作者: {}",
                    targetUser.getRealName(), targetUser.getPhone(), coachClass.getClassName(), loginUser.getUsername());

            return Result.OK("用户 " + targetUser.getRealName() + " 已成功加入班级 " + coachClass.getClassName());

        } catch (Exception e) {
            log.error("添加用户到班级失败", e);
            return Result.error("添加用户到班级失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param inzCoachClass
     * @return
     */
    @AutoLog(value = "教练班级-添加")
    @ApiOperation(value = "教练班级-添加", notes = "教练班级-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzCoachClass inzCoachClass) {
        // 获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有coach角色
        String userRoles = loginUser.getRoleCode();
        if(!userRoles.equals("admin")&&!userRoles.equals("coach")){
            return Result.error("您没有权限创建班级");
        }

        // 设置教练信息
        inzCoachClass.setCoachId(loginUser.getId());
        inzCoachClass.setCoachName(loginUser.getRealname());
        inzCoachClass.setCurrentStudents(0);
        inzCoachClass.setCreateBy(loginUser.getUsername());
        inzCoachClass.setStatus(1);

        inzCoachClass.setCreateTime(new Date());

        // 生成班级编码
        if (inzCoachClass.getClassCode() == null || inzCoachClass.getClassCode().isEmpty()) {
            String classCode = "CLASS_" + loginUser.getUsername() + "_" + System.currentTimeMillis();
            inzCoachClass.setClassCode(classCode);
        }

        inzCoachClassService.save(inzCoachClass);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzCoachClass
     * @return
     */
    @AutoLog(value = "班级表-编辑")
    @ApiOperation(value = "班级表-编辑", notes = "班级表-编辑")
    @RequiresPermissions("inz_coach_class:inz_coach_class:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzCoachClass inzCoachClass) {
        inzCoachClassService.updateById(inzCoachClass);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "班级表-通过id删除")
    @ApiOperation(value = "班级表-通过id删除", notes = "班级表-通过id删除")
    @RequiresPermissions("inz_coach_class:inz_coach_class:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzCoachClassService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "班级表-批量删除")
    @ApiOperation(value = "班级表-批量删除", notes = "班级表-批量删除")
    @RequiresPermissions("inz_coach_class:inz_coach_class:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzCoachClassService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "班级表-通过id查询")
    @ApiOperation(value = "班级表-通过id查询", notes = "班级表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzCoachClass> queryById(@RequestParam(name = "id", required = true) String id) {
        InzCoachClass inzCoachClass = inzCoachClassService.getById(id);
        if (inzCoachClass == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzCoachClass);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzCoachClass
     */
    @RequiresPermissions("inz_coach_class:inz_coach_class:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzCoachClass inzCoachClass) {
        return super.exportXls(request, inzCoachClass, InzCoachClass.class, "班级表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_coach_class:inz_coach_class:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzCoachClass.class);
    }

    /**
     * 添加用户到班级请求DTO
     */
    @Data
    public static class AddUserToClassRequest {
        private String classId;

        private String userId;
    }
}
