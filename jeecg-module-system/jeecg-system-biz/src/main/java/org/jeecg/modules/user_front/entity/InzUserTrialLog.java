package org.jeecg.modules.user_front.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户试用记录
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@ApiModel(value="inz_user_trial_log对象", description="用户试用记录")
@Data
@TableName("inz_user_trial_log")
public class InzUserTrialLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**用户ID（前台用户）*/
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**操作者ID（后台用户）*/
    @ApiModelProperty(value = "操作者ID")
    private String operatorId;

    /**操作描述*/
    @Excel(name = "操作描述", width = 30)
    @ApiModelProperty(value = "操作描述")
    private String content;

    /**试用天数变化量*/
    @Excel(name = "试用天数", width = 15)
    @ApiModelProperty(value = "试用天数变化量")
    private Integer trialDays;

    /**操作类型（1增加，0减少）*/
    @Excel(name = "操作类型", width = 15, dicCode = "trial_operation_type")
    @ApiModelProperty(value = "操作类型（1增加，0减少）")
    private Integer type;

    /**操作前剩余天数*/
    @Excel(name = "操作前剩余天数", width = 15)
    @ApiModelProperty(value = "操作前剩余天数")
    private Integer beforeDays;

    /**操作后剩余天数*/
    @Excel(name = "操作后剩余天数", width = 15)
    @ApiModelProperty(value = "操作后剩余天数")
    private Integer afterDays;

    /**教育系列ID*/
    @ApiModelProperty(value = "教育系列ID")
    private String educationSeriesId;

    /**来源类型*/
    @Excel(name = "来源类型", width = 15, dicCode = "trial_source_type")
    @ApiModelProperty(value = "来源类型")
    private String sourceType;
}
