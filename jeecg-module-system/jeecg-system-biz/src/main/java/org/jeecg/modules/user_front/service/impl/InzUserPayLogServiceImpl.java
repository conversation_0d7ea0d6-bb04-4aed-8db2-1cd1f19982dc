package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.mapper.InzUserPayLogMapper;
import org.jeecg.modules.user_front.service.IInzUserPayLogService;
import org.jeecg.modules.user_front.vo.InzUserPayLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzUserPayLogServiceImpl extends ServiceImpl<InzUserPayLogMapper, InzUserPayLog> implements IInzUserPayLogService {

	@Autowired
	private InzUserPayLogMapper inzUserPayLogMapper;

	@Override
	public List<InzUserPayLog> selectByMainId(String mainId) {
		return inzUserPayLogMapper.selectByMainId(mainId);
	}

	@Override
	public IPage<InzUserPayLogVO> selectEnhancedPayLogPage(Page<InzUserPayLogVO> page,
	                                                       String userId,
	                                                       String phone,
	                                                       String realName) {
		try {
			return inzUserPayLogMapper.selectEnhancedPayLogPage(page, userId, phone, realName);
		} catch (Exception e) {
			log.error("查询增强金豆记录分页失败", e);
			return page;
		}
	}

	@Override
	public List<InzUserPayLogVO> selectEnhancedPayLogByUserId(String userId, Integer limit) {
		try {
			return inzUserPayLogMapper.selectEnhancedPayLogByUserId(userId, limit != null ? limit : 20);
		} catch (Exception e) {
			log.error("查询用户增强金豆记录失败 - 用户ID: {}", userId, e);
			return Collections.emptyList();
		}
	}
}
