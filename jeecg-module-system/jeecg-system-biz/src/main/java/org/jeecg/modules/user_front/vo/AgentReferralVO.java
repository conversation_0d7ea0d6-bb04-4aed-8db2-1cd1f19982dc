package org.jeecg.modules.user_front.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 代理商推荐关系VO
 * @Author: Alex (工程师)
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@ApiModel(value="代理商推荐关系VO", description="代理商推荐用户的详细信息")
@Data
public class AgentReferralVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**用户ID*/
    @ApiModelProperty(value = "用户ID")
    private String id;

    /**真实姓名*/
    @ApiModelProperty(value = "真实姓名")
    private String realName;

    /**手机号*/
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**地址*/
    @ApiModelProperty(value = "地址")
    private String address;

    /**角色*/
    @ApiModelProperty(value = "角色")
    private String role;

    /**状态*/
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**金豆数量*/
    @ApiModelProperty(value = "金豆数量")
    private Integer goldenBean;

    /**邀请人ID*/
    @ApiModelProperty(value = "邀请人ID")
    private String parentId;

    /**邀请人姓名*/
    @ApiModelProperty(value = "邀请人姓名")
    private String parentName;

    /**邀请人手机号*/
    @ApiModelProperty(value = "邀请人手机号")
    private String parentPhone;

    /**推荐层级*/
    @ApiModelProperty(value = "推荐层级：1=直接推荐，2=间接推荐")
    private Integer level;

    /**注册时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "注册时间")
    private Date createTime;

    /**最后使用时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后使用时间")
    private Date lastUseAt;

    /**VIP时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "VIP时间")
    private Date vipTime;

    /**直接推荐用户数量*/
    @ApiModelProperty(value = "直接推荐用户数量")
    private Integer directReferralCount;

    /**间接推荐用户数量*/
    @ApiModelProperty(value = "间接推荐用户数量")
    private Integer indirectReferralCount;

    /**总推荐用户数量*/
    @ApiModelProperty(value = "总推荐用户数量")
    private Integer totalReferralCount;

    /**推荐路径*/
    @ApiModelProperty(value = "推荐路径")
    private String referralPath;
}

/**
 * 代理商推荐关系汇总VO
 */
@ApiModel(value="代理商推荐关系汇总VO", description="代理商推荐关系的汇总信息")
@Data
class AgentReferralSummaryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**代理商ID*/
    @ApiModelProperty(value = "代理商ID")
    private String agentId;

    /**代理商姓名*/
    @ApiModelProperty(value = "代理商姓名")
    private String agentName;

    /**代理商手机号*/
    @ApiModelProperty(value = "代理商手机号")
    private String agentPhone;

    /**直接推荐用户列表*/
    @ApiModelProperty(value = "直接推荐用户列表")
    private List<AgentReferralVO> directReferrals;

    /**间接推荐用户列表*/
    @ApiModelProperty(value = "间接推荐用户列表")
    private List<AgentReferralVO> indirectReferrals;

    /**统计信息*/
    @ApiModelProperty(value = "直接推荐用户数量")
    private Integer directCount;

    @ApiModelProperty(value = "间接推荐用户数量")
    private Integer indirectCount;

    @ApiModelProperty(value = "总推荐用户数量")
    private Integer totalCount;

    /**汇总金豆信息*/
    @ApiModelProperty(value = "直接推荐用户总金豆")
    private Integer directTotalBeans;

    @ApiModelProperty(value = "间接推荐用户总金豆")
    private Integer indirectTotalBeans;

    @ApiModelProperty(value = "所有推荐用户总金豆")
    private Integer allTotalBeans;
}
