<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.user_front.mapper.InzUserPayLogMapper">

    <!-- 增强金豆记录结果映射 -->
    <resultMap id="EnhancedPayLogResultMap" type="org.jeecg.modules.user_front.vo.InzUserPayLogVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="golden_bean" property="goldenBean"/>
        <result column="type" property="type"/>
        <result column="before_balance" property="beforeBalance"/>
        <result column="after_balance" property="afterBalance"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="source_user_id" property="sourceUserId"/>
        <result column="source_user_name" property="sourceUserName"/>
        <result column="source_user_phone" property="sourceUserPhone"/>
    </resultMap>

    <!-- 分页查询增强的金豆记录 -->
    <select id="selectEnhancedPayLogPage" resultMap="EnhancedPayLogResultMap">
        SELECT
            p.id,
            p.user_id,
            u.real_name,
            u.phone,
            p.golden_bean,
            p.type,
            p.before_balance,
            p.after_balance,
            p.content,
            p.create_time,
            p.create_by,
            p.source_user_id,
            su.real_name as source_user_name,
            su.phone as source_user_phone
        FROM inz_user_pay_log p
        LEFT JOIN inz_user_front u ON p.user_id = u.id
        LEFT JOIN inz_user_front su ON p.source_user_id = su.id
        <where>
            <if test="userId != null and userId != ''">
                AND p.user_id = #{userId}
            </if>
            <if test="phone != null and phone != ''">
                AND u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
        </where>
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询指定用户的增强金豆记录 -->
    <select id="selectEnhancedPayLogByUserId" resultMap="EnhancedPayLogResultMap">
        SELECT
            p.id,
            p.user_id,
            u.real_name,
            u.phone,
            p.golden_bean,
            p.type,
            p.before_balance,
            p.after_balance,
            p.content,
            p.create_time,
            p.create_by,
            p.source_user_id,
            su.real_name as source_user_name,
            su.phone as source_user_phone
        FROM inz_user_pay_log p
        LEFT JOIN inz_user_front u ON p.user_id = u.id
        LEFT JOIN inz_user_front su ON p.source_user_id = su.id
        WHERE p.user_id = #{userId}
        ORDER BY p.create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 原有的查询方法 -->
    <select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.user_front.entity.InzUserPayLog">
        SELECT
            id,
            create_by,
            create_time,
            update_by,
            update_time,
            sys_org_code,
            user_id,
            content,
            golden_bean,
            type,
            before_balance,
            after_balance,
            source_user_id
        FROM inz_user_pay_log
        WHERE user_id = #{mainId}
        ORDER BY create_time DESC
    </select>

    <!-- 删除方法 -->
    <delete id="deleteByMainId" parameterType="java.lang.String">
        DELETE FROM inz_user_pay_log WHERE user_id = #{mainId}
    </delete>

</mapper>
