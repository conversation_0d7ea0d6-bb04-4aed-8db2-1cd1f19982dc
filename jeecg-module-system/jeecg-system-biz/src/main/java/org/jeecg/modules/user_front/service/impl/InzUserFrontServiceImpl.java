package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.user_books.entity.AddUserBooksDto;
import org.jeecg.modules.user_front.entity.ChangePasswordDTO;
import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.vo.AgentReferralVO;
import org.jeecg.modules.user_front.vo.GoldenBeanStatsVO;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.user_front.mapper.InzUserDeviceMapper;
import org.jeecg.modules.user_front.mapper.InzUserFrontMapper;
import org.jeecg.modules.user_front.mapper.InzUserPayLogMapper;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzUserFrontServiceImpl extends ServiceImpl<InzUserFrontMapper, InzUserFront> implements IInzUserFrontService {
	@Autowired(required = false)
	private InzUserDeviceMapper inzUserDeviceMapper;
	@Autowired(required = false)
	private InzUserPayLogMapper inzUserPayLogMapper;
    @Autowired
    private RedisUtil redisUtil;
	@Autowired
    private ISysUserService sysUserService;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(InzUserFront inzUserFront, List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList) {
		baseMapper.insert(inzUserFront);
		if(inzUserDeviceList!=null && inzUserDeviceList.size()>0) {
			for(InzUserDevice entity:inzUserDeviceList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserDeviceMapper.insert(entity);
			}
		}
		if(inzUserPayLogList!=null && inzUserPayLogList.size()>0) {
			for(InzUserPayLog entity:inzUserPayLogList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserPayLogMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList) {
		baseMapper.updateById(inzUserFront);
		
		//1.先删除子表数据
		inzUserDeviceMapper.deleteByMainId(inzUserFront.getId());
		inzUserPayLogMapper.deleteByMainId(inzUserFront.getId());
		
		//2.子表数据重新插入
		if(inzUserDeviceList!=null && inzUserDeviceList.size()>0) {
			for(InzUserDevice entity:inzUserDeviceList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserDeviceMapper.insert(entity);
			}
		}
		if(inzUserPayLogList!=null && inzUserPayLogList.size()>0) {
			for(InzUserPayLog entity:inzUserPayLogList) {
				//外键设置
				entity.setUserId(inzUserFront.getId());
				inzUserPayLogMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		inzUserDeviceMapper.deleteByMainId(id);
		inzUserPayLogMapper.deleteByMainId(id);
		baseMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			inzUserDeviceMapper.deleteByMainId(id.toString());
			inzUserPayLogMapper.deleteByMainId(id.toString());
			baseMapper.deleteById(id);
		}
	}

	@Override
	public void verifyAndDeduct(String id, int totalCost, AddUserBooksDto addUserBooksDto, int size) throws Exception {
		InzUserFront userFront = baseMapper.selectById(id);
		Integer originGoldenBean = userFront.getGoldenBean();
		List<String> userIds = Arrays.asList(addUserBooksDto.getUserIds().split(","));
		List<String> bookIds = Arrays.asList(addUserBooksDto.getWordBookIds().split(","));
		userFront.setGoldenBean(userFront.getGoldenBean() - totalCost);
		if (userFront.getGoldenBean() == 0) {
			throw new Exception("金豆不足");
		}
		baseMapper.update(userFront, new UpdateWrapper<InzUserFront>().lambda().eq(InzUserFront::getId, id).ge(InzUserFront::getGoldenBean, totalCost));
		// 记录金豆变动日志
		InzUserPayLog log = new InzUserPayLog();
		log.setUserId(userFront.getId());
		log.setGoldenBean(-totalCost); // 扣减为负值
		log.setType(0); // 0表示减少

		// 构建更详细的日志内容
		StringBuilder content = new StringBuilder();
		content.append(String.format("为%d个用户购买%d本图书，扣减%d金豆 (原余额:%d → 新余额:%d)\n",
				userIds.size(),
				size,
				totalCost,
				originGoldenBean,
				userFront.getGoldenBean()));

		// 添加分配目标用户信息
		content.append("分配目标用户ID: ");
		if(userIds.size() <= 5) {
			// 用户数量少时显示全部ID
			content.append(String.join(", ", userIds));
		} else {
			// 用户数量多时显示前3个+总数
			content.append(String.join(", ", userIds.subList(0, 3)))
					.append("...等").append(userIds.size()).append("个用户");
		}

		// 添加分配的图书信息（可选）
		content.append("\n分配图书ID: ");
		if(bookIds.size() <= 3) {
			content.append(String.join(", ", bookIds));
		} else {
			content.append(String.join(", ", bookIds.subList(0, 2)))
					.append("...等").append(bookIds.size()).append("本图书");
		}

		log.setContent(content.toString());
		log.setCreateTime(new Date());
		inzUserPayLogMapper.insert(log);
	}

	@Override
	public boolean changePassword(ChangePasswordDTO changePasswordDTO) {
		InzUserFront userFront = baseMapper.selectById(changePasswordDTO.getUserId());
		if(userFront != null) {
			List<InzUserDevice> inzUserDevices = inzUserDeviceMapper.selectList(new QueryWrapper<InzUserDevice>().lambda()
					.eq(InzUserDevice::getUserId, changePasswordDTO.getUserId()));
			inzUserDevices.forEach(inzUserDevice -> {
				redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + inzUserDevice.getToken());
			});
			String passwordEncode = PasswordUtil.encrypt(userFront.getPhone(), userFront.getPassword(), userFront.getSalt());
			userFront.setPassword(passwordEncode);
			return baseMapper.updateById(userFront) > 0;
		}
		return false;
	}

	@Override
	public List<AgentReferralVO> getDirectReferrals(String agentId) {
		try {
			return baseMapper.selectDirectReferrals(agentId);
		} catch (Exception e) {
			log.error("查询直接推荐用户失败 - 代理商ID: " + agentId, e);
			return Collections.emptyList();
		}
	}

	@Override
	public List<AgentReferralVO> getIndirectReferrals(String agentId) {
		try {
			return baseMapper.selectIndirectReferrals(agentId);
		} catch (Exception e) {
			log.error("查询间接推荐用户失败 - 代理商ID: " + agentId, e);
			return Collections.emptyList();
		}
	}

	@Override
	public List<AgentReferralVO> getAllReferrals(String agentId) {
		try {
			return baseMapper.selectAllReferrals(agentId);
		} catch (Exception e) {
			log.error("查询所有推荐用户失败 - 代理商ID: " + agentId, e);
			return Collections.emptyList();
		}
	}

	@Override
	public GoldenBeanStatsVO getGoldenBeanStats(String userId) {
		try {
			log.debug("开始获取用户金币统计信息 - 用户ID: {}", userId);

			// 1. 获取总计金币（所有收入记录）
			Integer totalGoldenBean = getTotalGoldenBean(userId);

			// 2. 获取开户使用金币（支出记录中包含开通关键字的）
			Integer usedGoldenBean = getUsedGoldenBean(userId);

			// 3. 获取剩余金币（当前余额）
			Integer remainingGoldenBean = getRemainingGoldenBean(userId);

			// 4. 组装统计结果
			GoldenBeanStatsVO stats = new GoldenBeanStatsVO();
			stats.setTotalGoldenBean(totalGoldenBean);
			stats.setUsedGoldenBean(usedGoldenBean);
			stats.setRemainingGoldenBean(remainingGoldenBean);
			stats.setStatsTime(new Date());

			log.debug("获取用户金币统计成功 - 用户ID: {}, 总计: {}, 使用: {}, 剩余: {}",
					userId, totalGoldenBean, usedGoldenBean, remainingGoldenBean);

			return stats;

		} catch (Exception e) {
			log.error("获取用户金币统计失败 - 用户ID: {}", userId, e);
			return GoldenBeanStatsVO.createDefault();
		}
	}

	/**
	 * 获取总计金币（所有收入记录）
	 */
	private Integer getTotalGoldenBean(String userId) {
		try {
			LambdaQueryWrapper<InzUserPayLog> query = new LambdaQueryWrapper<>();
			query.eq(InzUserPayLog::getUserId, userId)
				 .eq(InzUserPayLog::getType, 1); // type=1 表示收入

			List<InzUserPayLog> logs = inzUserPayLogMapper.selectList(query);
			return logs.stream()
					   .mapToInt(log -> log.getGoldenBean() != null ? log.getGoldenBean() : 0)
					   .sum();
		} catch (Exception e) {
			log.error("获取总计金币失败 - 用户ID: {}", userId, e);
			return 0;
		}
	}

	/**
	 * 获取开户使用金币（支出记录中包含开通关键字的）
	 */
	private Integer getUsedGoldenBean(String userId) {
		try {
			LambdaQueryWrapper<InzUserPayLog> query = new LambdaQueryWrapper<>();
			query.eq(InzUserPayLog::getUserId, userId)
				 .eq(InzUserPayLog::getType, 0) // type=0 表示支出
				 .and(wrapper -> wrapper.like(InzUserPayLog::getContent, "开通")
									   .or()
									   .like(InzUserPayLog::getContent, "开户"));

			List<InzUserPayLog> logs = inzUserPayLogMapper.selectList(query);
			return logs.stream()
					   .mapToInt(log -> log.getGoldenBean() != null ? log.getGoldenBean() : 0)
					   .sum();
		} catch (Exception e) {
			log.error("获取开户使用金币失败 - 用户ID: {}", userId, e);
			return 0;
		}
	}

	/**
	 * 获取剩余金币（当前余额）
	 */
	private Integer getRemainingGoldenBean(String userId) {
		try {
			InzUserFront user = baseMapper.selectById(userId);
			return user != null && user.getGoldenBean() != null ? user.getGoldenBean() : 0;
		} catch (Exception e) {
			log.error("获取剩余金币失败 - 用户ID: {}", userId, e);
			return 0;
		}
	}

	@Override
	public InzUserFront getByBackendUserId(String backendUserId) {
		try {
			log.debug("根据后台用户ID查找前台用户 - 后台用户ID: {}", backendUserId);
			try {
				// 查询后台用户信息
				LambdaQueryWrapper<SysUser> sysUserQuery = new LambdaQueryWrapper<>();
				sysUserQuery.eq(SysUser::getId, backendUserId);
				SysUser sysUser = sysUserService.getOne(sysUserQuery);

				if (sysUser == null || sysUser.getPhone() == null) {
					log.warn("后台用户不存在或手机号为空 - 后台用户ID: {}", backendUserId);
					return null;
				}

				String phone = sysUser.getPhone();

				// 查询前台用户信息 - 使用传统QueryWrapper避免Lambda缓存问题
				QueryWrapper<InzUserFront> frontUserQuery = new QueryWrapper<>();
				frontUserQuery.eq("phone", phone);
				InzUserFront frontendUser = this.getOne(frontUserQuery);

				if (frontendUser != null) {
					log.debug("找到对应的前台用户 - 后台用户ID: {}, 前台用户ID: {}, 角色: {}",
							backendUserId, frontendUser.getId(), frontendUser.getRole());
					return frontendUser;
				}

				log.warn("未找到后台用户对应的前台用户 - 后台用户ID: {}, 手机号: {}", backendUserId, phone);
				return null;

			} catch (Exception e) {
				log.error("查询后台用户手机号失败 - 后台用户ID: {}", backendUserId, e);
				return null;
			}

		} catch (Exception e) {
			log.error("根据后台用户ID查找前台用户失败 - 后台用户ID: {}", backendUserId, e);
			return null;
		}
	}
}
