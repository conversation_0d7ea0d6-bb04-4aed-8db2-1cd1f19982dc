package org.jeecg.modules.inz_coach_class_student.service.impl;

import org.jeecg.modules.inz_coach_class_student.entity.InzCoachClassStudent;
import org.jeecg.modules.inz_coach_class_student.mapper.InzCoachClassStudentMapper;
import org.jeecg.modules.inz_coach_class_student.service.IInzCoachClassStudentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 班级学生表
 * @Author: jeecg-boot
 * @Date:   2025-08-02
 * @Version: V1.0
 */
@Service
public class InzCoachClassStudentServiceImpl extends ServiceImpl<InzCoachClassStudentMapper, InzCoachClassStudent> implements IInzCoachClassStudentService {

}
