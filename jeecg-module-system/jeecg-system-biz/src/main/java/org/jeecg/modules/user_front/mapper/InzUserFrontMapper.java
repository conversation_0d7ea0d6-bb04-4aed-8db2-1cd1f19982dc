package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.vo.AgentReferralVO;

import java.util.List;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface InzUserFrontMapper extends BaseMapper<InzUserFront> {

	/**
	 * 查询代理商的直接推荐用户
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> selectDirectReferrals(@Param("agentId") String agentId);

	/**
	 * 查询代理商的间接推荐用户（二级推荐）
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> selectIndirectReferrals(@Param("agentId") String agentId);

	/**
	 * 查询代理商的所有推荐用户（直接+间接）
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> selectAllReferrals(@Param("agentId") String agentId);
}
