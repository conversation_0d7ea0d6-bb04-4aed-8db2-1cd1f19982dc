import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '班级ID',
    align:"center",
    dataIndex: 'classId'
   },
   {
    title: '学生ID（用户ID）',
    align:"center",
    dataIndex: 'studentId'
   },
   {
    title: '学生姓名',
    align:"center",
    dataIndex: 'studentName'
   },
   {
    title: '学生电话',
    align:"center",
    dataIndex: 'studentPhone'
   },
   {
    title: '加入时间',
    align:"center",
    dataIndex: 'joinTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '班级ID',
    field: 'classId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入班级ID!'},
          ];
     },
  },
  {
    label: '学生ID（用户ID）',
    field: 'studentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学生ID（用户ID）!'},
          ];
     },
  },
  {
    label: '学生姓名',
    field: 'studentName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学生姓名!'},
          ];
     },
  },
  {
    label: '学生电话',
    field: 'studentPhone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学生电话!'},
          ];
     },
  },
  {
    label: '加入时间',
    field: 'joinTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入加入时间!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  classId: {title: '班级ID',order: 0,view: 'text', type: 'string',},
  studentId: {title: '学生ID（用户ID）',order: 1,view: 'text', type: 'string',},
  studentName: {title: '学生姓名',order: 2,view: 'text', type: 'string',},
  studentPhone: {title: '学生电话',order: 3,view: 'text', type: 'string',},
  joinTime: {title: '加入时间',order: 4,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}