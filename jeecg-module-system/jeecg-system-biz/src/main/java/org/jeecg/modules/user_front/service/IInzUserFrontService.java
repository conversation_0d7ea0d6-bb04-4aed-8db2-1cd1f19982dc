package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_books.entity.AddUserBooksDto;
import org.jeecg.modules.user_front.entity.ChangePasswordDTO;
import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.vo.AgentReferralVO;
import org.jeecg.modules.user_front.vo.GoldenBeanStatsVO;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface IInzUserFrontService extends IService<InzUserFront> {

	/**
	 * 添加一对多
	 *
	 * @param inzUserFront
	 * @param inzUserDeviceList
	 * @param inzUserPayLogList
	 */
	public void saveMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param inzUserFront
	 * @param inzUserDeviceList
	 * @param inzUserPayLogList
	 */
	public void updateMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);


	void verifyAndDeduct(String id, int totalCost, @Valid AddUserBooksDto addUserBooksDto, int size) throws Exception;

	boolean changePassword(ChangePasswordDTO changePasswordDTO);

	/**
	 * 查询代理商的直接推荐用户
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> getDirectReferrals(String agentId);

	/**
	 * 查询代理商的间接推荐用户（二级推荐）
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> getIndirectReferrals(String agentId);

	/**
	 * 查询代理商的所有推荐用户（直接+间接）
	 *
	 * @param agentId 代理商ID
	 * @return List<AgentReferralVO>
	 */
	List<AgentReferralVO> getAllReferrals(String agentId);

	/**
	 * 获取代理商金币统计信息
	 *
	 * @param userId 用户ID
	 * @return GoldenBeanStatsVO 金币统计信息
	 */
	GoldenBeanStatsVO getGoldenBeanStats(String userId);

	/**
	 * 根据后台用户ID查找对应的前台用户
	 *
	 * @param backendUserId 后台用户ID
	 * @return InzUserFront 前台用户信息
	 */
	InzUserFront getByBackendUserId(String backendUserId);
}
