package org.jeecg.modules.inz_words_articles.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.inz_words_articles.entity.InzWordsArticles;

import java.util.List;

/**
 * @Description: 短文表
 * @Author: jeecg-boot
 * @Date: 2025-07-06
 * @Version: V1.0
 */
public interface IInzWordsArticlesService extends IService<InzWordsArticles> {

    /**
     * 获取章节中已有短文包含的单词ID集合
     *
     * @param bookId    词书ID
     * @param chapterId 章节ID
     * @return 单词ID列表
     */
    List<String> getWordIdsInChapterArticles(String bookId, String chapterId);

    /**
     * 为指定单词生成并保存短文
     *
     * @param wordIds    单词ID列表
     * @param words      单词列表
     * @param bookId     词书ID
     * @param chapterId  章节ID
     * @param groupIndex 分组索引
     * @param count      生成短文数量
     * @return 生成的短文数量
     */
    int generateAndSaveArticles(List<String> wordIds, List<String> words, String bookId, String chapterId, int groupIndex, int count, String fileName);

}
