package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.user_front.entity.InzPermissionGrantLog;
import org.jeecg.modules.user_front.mapper.InzPermissionGrantLogMapper;
import org.jeecg.modules.user_front.service.IInzPermissionGrantLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 权限开通记录Service实现类
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzPermissionGrantLogServiceImpl extends ServiceImpl<InzPermissionGrantLogMapper, InzPermissionGrantLog> implements IInzPermissionGrantLogService {

    @Autowired
    private InzPermissionGrantLogMapper permissionGrantLogMapper;

    @Override
    public boolean createGrantRecord(String userId, String operatorId, String educationId, String educationName,
                                    String grantType, Integer goldenBeanCost, Integer wordBookCount,
                                    Date grantTime, Date expirationTime) {
        try {
            log.info("创建权限开通记录 - 用户: {}, 操作者: {}, 教育阶段: {}, 金币: {}, 单词书数量: {}",
                    userId, operatorId, educationName, goldenBeanCost, wordBookCount);

            InzPermissionGrantLog grantLog = new InzPermissionGrantLog();
            grantLog.setUserId(userId);
            grantLog.setOperatorId(operatorId);
            grantLog.setEducationId(educationId);
            grantLog.setEducationName(educationName);
            grantLog.setGrantType(grantType);
            grantLog.setGoldenBeanCost(goldenBeanCost);
            grantLog.setWordBookCount(wordBookCount);
            grantLog.setGrantTime(grantTime);
            grantLog.setExpirationTime(expirationTime);
            grantLog.setStatus("active"); // 有效状态
            grantLog.setCreateTime(new Date());
            grantLog.setCreateBy(operatorId);

            boolean result = this.save(grantLog);
            if (result) {
                log.info("权限开通记录创建成功 - 记录ID: {}", grantLog.getId());
            } else {
                log.error("权限开通记录创建失败 - 用户: {}", userId);
            }
            return result;

        } catch (Exception e) {
            log.error("创建权限开通记录异常 - 用户: {}, 操作者: {}", userId, operatorId, e);
            return false;
        }
    }

    @Override
    public List<InzPermissionGrantLog> getUserGrantRecords(String userId) {
        try {
            return permissionGrantLogMapper.selectByUserId(userId);
        } catch (Exception e) {
            log.error("查询用户权限开通记录失败 - 用户: {}", userId, e);
            return null;
        }
    }

    @Override
    public List<InzPermissionGrantLog> getUserActiveGrantRecords(String userId) {
        try {
            return permissionGrantLogMapper.selectActiveByUserId(userId);
        } catch (Exception e) {
            log.error("查询用户有效权限开通记录失败 - 用户: {}", userId, e);
            return null;
        }
    }

    @Override
    public InzPermissionGrantLog getLatestGrantRecord(String userId) {
        try {
            return permissionGrantLogMapper.selectLatestActiveByUserId(userId);
        } catch (Exception e) {
            log.error("查询用户最新权限开通记录失败 - 用户: {}", userId, e);
            return null;
        }
    }

    @Override
    public InzPermissionGrantLog getGrantRecordByUserAndEducation(String userId, String educationId) {
        try {
            return permissionGrantLogMapper.selectByUserAndEducation(userId, educationId);
        } catch (Exception e) {
            log.error("查询用户教育阶段权限记录失败 - 用户: {}, 教育阶段: {}", userId, educationId, e);
            return null;
        }
    }

    @Override
    public boolean updateGrantRecordStatus(String recordId, String status, String updateBy) {
        try {
            int result = permissionGrantLogMapper.updateStatus(recordId, status, updateBy);
            log.info("更新权限记录状态 - 记录ID: {}, 状态: {}, 结果: {}", recordId, status, result > 0);
            return result > 0;
        } catch (Exception e) {
            log.error("更新权限记录状态失败 - 记录ID: {}, 状态: {}", recordId, status, e);
            return false;
        }
    }

    @Override
    public int markUserPermissionsAsRefunded(String userId, String updateBy) {
        try {
            int result = permissionGrantLogMapper.updateUserPermissionsToRefunded(userId, updateBy);
            log.info("标记用户权限为已退单 - 用户: {}, 更新数量: {}", userId, result);
            return result;
        } catch (Exception e) {
            log.error("标记用户权限为已退单失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    @Override
    public boolean markEducationPermissionAsRefunded(String userId, String educationId, String updateBy) {
        try {
            InzPermissionGrantLog record = getGrantRecordByUserAndEducation(userId, educationId);
            if (record != null) {
                return updateGrantRecordStatus(record.getId(), "refunded", updateBy);
            }
            log.warn("未找到对应的权限记录 - 用户: {}, 教育阶段: {}", userId, educationId);
            return false;
        } catch (Exception e) {
            log.error("标记教育阶段权限为已退单失败 - 用户: {}, 教育阶段: {}", userId, educationId, e);
            return false;
        }
    }

    @Override
    public Integer getOperatorGrantCount(String operatorId) {
        try {
            return permissionGrantLogMapper.countByOperatorId(operatorId);
        } catch (Exception e) {
            log.error("统计操作者权限开通数量失败 - 操作者: {}", operatorId, e);
            return 0;
        }
    }

    @Override
    public Integer getOperatorTotalGoldenBean(String operatorId) {
        try {
            return permissionGrantLogMapper.sumGoldenBeanByOperatorId(operatorId);
        } catch (Exception e) {
            log.error("统计操作者总金币失败 - 操作者: {}", operatorId, e);
            return 0;
        }
    }

    @Override
    public List<InzPermissionGrantLog> getGrantRecordsByTimeRange(String startTime, String endTime) {
        try {
            return permissionGrantLogMapper.selectByTimeRange(startTime, endTime);
        } catch (Exception e) {
            log.error("查询时间范围内权限开通记录失败 - 开始: {}, 结束: {}", startTime, endTime, e);
            return null;
        }
    }
}
