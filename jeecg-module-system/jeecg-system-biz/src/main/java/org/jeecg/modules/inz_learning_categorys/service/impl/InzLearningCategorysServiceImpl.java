package org.jeecg.modules.inz_learning_categorys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.mapper.InzLearningCategorysMapper;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.jeecg.modules.inz_learning_videos.entity.InzLearningVideos;
import org.jeecg.modules.inz_learning_videos.service.IInzLearningVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 继续深造-分类
 * @Author: jeecg-boot
 * @Date:   2025-08-03
 * @Version: V1.0
 */
@Slf4j
@Service
public class InzLearningCategorysServiceImpl extends ServiceImpl<InzLearningCategorysMapper, InzLearningCategorys> implements IInzLearningCategorysService {

    @Autowired
    private IInzLearningVideosService inzLearningVideosService;

    @Override
    public List<InzLearningCategorys> getSubCategories(String parentId) {
        QueryWrapper<InzLearningCategorys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        // 移除硬编码的level和status限制，让查询更灵活
        // queryWrapper.eq("level", 2); // 二级分类
        queryWrapper.eq("status", 1); // 只保留启用状态的限制
        queryWrapper.orderByAsc("sort_order");
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public boolean cascadeDeleteCategory(String categoryId) {
        try {
            log.info("开始级联删除分类 - 分类ID: {}", categoryId);

            // 1. 验证分类是否存在
            InzLearningCategorys category = this.getById(categoryId);
            if (category == null) {
                log.warn("分类不存在 - ID: {}", categoryId);
                return false;
            }

            // 2. 递归删除所有子分类
            List<InzLearningCategorys> subCategories = getSubCategories(categoryId);
            for (InzLearningCategorys subCategory : subCategories) {
                // 递归删除子分类
                boolean subDeleteResult = cascadeDeleteCategory(subCategory.getId());
                if (!subDeleteResult) {
                    log.error("删除子分类失败 - 子分类ID: {}, 父分类ID: {}", subCategory.getId(), categoryId);
                    return false;
                }
            }

            // 3. 检查当前分类是否有关联的视频
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", categoryId);
            long videoCount = inzLearningVideosService.count(videoWrapper);

            if (videoCount > 0) {
                log.warn("分类下有关联视频，无法删除 - 分类ID: {}, 视频数量: {}", categoryId, videoCount);
                return false;
            }

            // 4. 删除当前分类
            boolean deleteResult = this.removeById(categoryId);
            if (deleteResult) {
                log.info("分类删除成功 - ID: {}, 名称: {}", categoryId, category.getCategoryName());
            } else {
                log.error("分类删除失败 - ID: {}", categoryId);
            }

            return deleteResult;

        } catch (Exception e) {
            log.error("级联删除分类失败 - 分类ID: {}", categoryId, e);
            return false;
        }
    }

    @Override
    public boolean canDeleteCategory(String categoryId) {
        try {
            // 1. 检查是否有关联的视频
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", categoryId);
            long videoCount = inzLearningVideosService.count(videoWrapper);

            if (videoCount > 0) {
                log.info("分类下有关联视频，不能删除 - 分类ID: {}, 视频数量: {}", categoryId, videoCount);
                return false;
            }

            // 2. 检查是否有子分类
            QueryWrapper<InzLearningCategorys> subCategoryWrapper = new QueryWrapper<>();
            subCategoryWrapper.eq("parent_id", categoryId);
            long subCategoryCount = this.count(subCategoryWrapper);

            if (subCategoryCount > 0) {
                log.info("分类下有子分类，不能删除 - 分类ID: {}, 子分类数量: {}", categoryId, subCategoryCount);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查分类删除条件失败 - 分类ID: {}", categoryId, e);
            return false;
        }
    }
}
