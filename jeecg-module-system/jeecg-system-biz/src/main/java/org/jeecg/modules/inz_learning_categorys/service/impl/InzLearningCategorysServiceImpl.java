package org.jeecg.modules.inz_learning_categorys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.mapper.InzLearningCategorysMapper;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.jeecg.modules.inz_learning_modules.entity.InzLearningModules;
import org.jeecg.modules.inz_learning_modules.service.IInzLearningModulesService;
import org.jeecg.modules.inz_learning_videos.entity.InzLearningVideos;
import org.jeecg.modules.inz_learning_videos.service.IInzLearningVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 继续深造-分类
 * @Author: jeecg-boot
 * @Date:   2025-08-03
 * @Version: V1.0
 */
@Slf4j
@Service
public class InzLearningCategorysServiceImpl extends ServiceImpl<InzLearningCategorysMapper, InzLearningCategorys> implements IInzLearningCategorysService {

    @Autowired
    private IInzLearningVideosService inzLearningVideosService;

    @Autowired
    private IInzLearningModulesService inzLearningModulesService;

    @Override
    public List<InzLearningCategorys> getSubCategories(String parentId) {
        QueryWrapper<InzLearningCategorys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        // 移除硬编码的level和status限制，让查询更灵活
        // queryWrapper.eq("level", 2); // 二级分类
        queryWrapper.eq("status", 1); // 只保留启用状态的限制
        queryWrapper.orderByAsc("sort_order");
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public boolean cascadeDeleteCategory(String categoryId) {
        try {
            log.info("开始级联删除分类 - 分类ID: {}", categoryId);

            // 1. 验证分类是否存在
            InzLearningCategorys category = this.getById(categoryId);
            if (category == null) {
                log.warn("分类不存在 - ID: {}", categoryId);
                return false;
            }

            // 2. 递归删除所有子分类
            List<InzLearningCategorys> subCategories = getSubCategories(categoryId);
            for (InzLearningCategorys subCategory : subCategories) {
                // 递归删除子分类
                boolean subDeleteResult = cascadeDeleteCategory(subCategory.getId());
                if (!subDeleteResult) {
                    log.error("删除子分类失败 - 子分类ID: {}, 父分类ID: {}", subCategory.getId(), categoryId);
                    return false;
                }
            }

            // 3. 删除当前分类下的关联视频
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", categoryId);
            List<InzLearningVideos> relatedVideos = inzLearningVideosService.list(videoWrapper);

            if (!relatedVideos.isEmpty()) {
                log.info("发现分类下有 {} 个关联视频，开始删除 - 分类ID: {}", relatedVideos.size(), categoryId);

                // 删除所有关联视频
                boolean videoDeleteResult = inzLearningVideosService.remove(videoWrapper);
                if (!videoDeleteResult) {
                    log.error("删除分类下的关联视频失败 - 分类ID: {}", categoryId);
                    return false;
                }

                // 更新分类和模块的视频统计
                updateVideoStatisticsAfterDelete(relatedVideos);

                log.info("成功删除分类下的 {} 个关联视频 - 分类ID: {}", relatedVideos.size(), categoryId);
            }

            // 4. 删除当前分类
            boolean deleteResult = this.removeById(categoryId);
            if (deleteResult) {
                log.info("分类删除成功 - ID: {}, 名称: {}", categoryId, category.getCategoryName());
            } else {
                log.error("分类删除失败 - ID: {}", categoryId);
            }

            return deleteResult;

        } catch (Exception e) {
            log.error("级联删除分类失败 - 分类ID: {}", categoryId, e);
            return false;
        }
    }

    @Override
    public boolean canDeleteCategory(String categoryId) {
        try {
            // 注意：现在我们支持级联删除视频和子分类，所以这个方法主要用于其他业务逻辑检查

            // 1. 检查分类是否存在
            InzLearningCategorys category = this.getById(categoryId);
            if (category == null) {
                log.info("分类不存在 - 分类ID: {}", categoryId);
                return false;
            }

            // 2. 记录关联数据信息（用于日志）
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", categoryId);
            long videoCount = inzLearningVideosService.count(videoWrapper);

            QueryWrapper<InzLearningCategorys> subCategoryWrapper = new QueryWrapper<>();
            subCategoryWrapper.eq("parent_id", categoryId);
            long subCategoryCount = this.count(subCategoryWrapper);

            log.info("分类删除检查 - 分类ID: {}, 关联视频: {}, 子分类: {}",
                    categoryId, videoCount, subCategoryCount);

            // 3. 现在支持级联删除，所以总是返回true（除非分类不存在）
            return true;

        } catch (Exception e) {
            log.error("检查分类删除条件失败 - 分类ID: {}", categoryId, e);
            return false;
        }
    }

    /**
     * 删除视频后更新统计信息
     * @param deletedVideos 被删除的视频列表
     */
    private void updateVideoStatisticsAfterDelete(List<InzLearningVideos> deletedVideos) {
        try {
            if (deletedVideos == null || deletedVideos.isEmpty()) {
                return;
            }

            log.info("开始更新视频删除后的统计信息 - 删除视频数量: {}", deletedVideos.size());

            // 按分类ID分组统计
            Map<String, Long> categoryVideoCount = deletedVideos.stream()
                    .collect(Collectors.groupingBy(InzLearningVideos::getCategoryId, Collectors.counting()));

            // 按模块ID分组统计
            Map<String, Long> moduleVideoCount = new HashMap<>();

            for (Map.Entry<String, Long> entry : categoryVideoCount.entrySet()) {
                String categoryId = entry.getKey();
                Long videoCount = entry.getValue();

                // 更新分类的视频统计
                updateCategoryVideoCount(categoryId, -videoCount.intValue());

                // 获取分类信息以更新模块统计
                InzLearningCategorys category = this.getById(categoryId);
                if (category != null && category.getModuleId() != null) {
                    moduleVideoCount.merge(category.getModuleId(), videoCount, Long::sum);
                }
            }

            // 更新模块的视频统计
            for (Map.Entry<String, Long> entry : moduleVideoCount.entrySet()) {
                String moduleId = entry.getKey();
                Long videoCount = entry.getValue();
                updateModuleVideoCount(moduleId, -videoCount.intValue());
            }

            log.info("视频删除后统计信息更新完成 - 影响分类: {}, 影响模块: {}",
                    categoryVideoCount.size(), moduleVideoCount.size());

        } catch (Exception e) {
            log.error("更新视频删除后的统计信息失败", e);
        }
    }

    /**
     * 更新分类的视频数量统计
     * @param categoryId 分类ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateCategoryVideoCount(String categoryId, int deltaCount) {
        try {
            InzLearningCategorys category = this.getById(categoryId);
            if (category != null) {
                int currentCount = category.getTotalVideos() != null ? category.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                category.setTotalVideos(newCount);
                category.setUpdateTime(new Date());
                this.updateById(category);

                log.info("更新分类视频统计 - 分类ID: {}, 原数量: {}, 变化: {}, 新数量: {}",
                        categoryId, currentCount, deltaCount, newCount);

                // 如果是子分类，还需要更新父分类的统计
                if (category.getParentId() != null && !"0".equals(category.getParentId())) {
                    updateCategoryVideoCount(category.getParentId(), deltaCount);
                }
            }
        } catch (Exception e) {
            log.error("更新分类视频统计失败 - 分类ID: {}, 变化数量: {}", categoryId, deltaCount, e);
        }
    }

    /**
     * 更新模块的视频数量统计
     * @param moduleId 模块ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateModuleVideoCount(String moduleId, int deltaCount) {
        try {
            InzLearningModules module = inzLearningModulesService.getById(moduleId);
            if (module != null) {
                int currentCount = module.getTotalVideos() != null ? module.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                module.setTotalVideos(newCount);
                module.setUpdateTime(new Date());
                inzLearningModulesService.updateById(module);

                log.info("更新模块视频统计 - 模块ID: {}, 原数量: {}, 变化: {}, 新数量: {}",
                        moduleId, currentCount, deltaCount, newCount);
            }
        } catch (Exception e) {
            log.error("更新模块视频统计失败 - 模块ID: {}, 变化数量: {}", moduleId, deltaCount, e);
        }
    }
}
