package org.jeecg.modules.inz_learning_categorys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.mapper.InzLearningCategorysMapper;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 继续深造-分类
 * @Author: jeecg-boot
 * @Date:   2025-08-03
 * @Version: V1.0
 */
@Service
public class InzLearningCategorysServiceImpl extends ServiceImpl<InzLearningCategorysMapper, InzLearningCategorys> implements IInzLearningCategorysService {

    @Override
    public List<InzLearningCategorys> getSubCategories(String parentId) {
        QueryWrapper<InzLearningCategorys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        // 移除硬编码的level和status限制，让查询更灵活
        // queryWrapper.eq("level", 2); // 二级分类
        queryWrapper.eq("status", 1); // 只保留启用状态的限制
        queryWrapper.orderByAsc("sort_order");
        return this.list(queryWrapper);
    }
}
