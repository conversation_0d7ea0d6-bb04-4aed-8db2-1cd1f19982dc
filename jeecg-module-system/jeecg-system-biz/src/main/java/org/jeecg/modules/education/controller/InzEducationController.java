package org.jeecg.modules.education.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.education.entity.InzEducation;
import org.jeecg.modules.education.service.IInzEducationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

 /**
 * @Description: 教育阶段
 * @Author: jeecg-boot
 * @Date:   2025-03-19
 * @Version: V1.0
 */
@Api(tags="教育阶段")
@RestController
@RequestMapping("/education/inzEducation")
@Slf4j
public class InzEducationController extends JeecgController<InzEducation, IInzEducationService>{
	@Autowired
	private IInzEducationService inzEducationService;

	/**
	 * 分页列表查询
	 *
	 * @param inzEducation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "教育阶段-分页列表查询")
	@ApiOperation(value="教育阶段-分页列表查询", notes="教育阶段-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<IPage<InzEducation>> queryPageList(InzEducation inzEducation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<InzEducation> queryWrapper =  new QueryWrapper<InzEducation>();
			queryWrapper.lambda().orderByAsc(InzEducation::getSort);
            List<InzEducation> list = inzEducationService.queryTreeListNoPage(queryWrapper);
            IPage<InzEducation> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = inzEducation.getPid();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            inzEducation.setPid(null);
            QueryWrapper<InzEducation> queryWrapper = new QueryWrapper<InzEducation>();
			queryWrapper.lambda().orderByAsc(InzEducation::getSort);
            // 使用 eq 防止模糊查询
            queryWrapper.eq("pid", parentId);
            Page<InzEducation> page = new Page<InzEducation>(pageNo, pageSize);
            IPage<InzEducation> pageList = inzEducationService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}

	 @ApiOperation(value="教育阶段-根据ids查询数据", notes="教育阶段-根据ids查询数据")
	 @GetMapping(value = "/listByCategoryIds")
	 public Result<List<InzEducation>> listByCategoryIds(@RequestParam String ids,
												 HttpServletRequest req) {
		 List<InzEducation> pageList = inzEducationService.list(new QueryWrapper<InzEducation>().lambda().in(InzEducation::getId, Arrays.asList(ids.split(","))).orderBy(true,true,InzEducation::getSort));
		 return Result.OK(pageList);
	 }

	 /**
	  * 【vue3专用】加载节点的子数据
	  *
	  * @param pid
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = inzEducationService.queryListByPid(pid);
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】加载一级节点/如果是同步 则所有数据
	  *
	  * @param async
	  * @param pcode
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = inzEducationService.queryListByCode(pcode);
			 if (!async) {
				 loadAllChildren(ls);
			 }
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】递归求子节点 同步加载用到
	  *
	  * @param ls
	  */
	 private void loadAllChildren(List<SelectTreeModel> ls) {
		 for (SelectTreeModel tsm : ls) {
			 List<SelectTreeModel> temp = inzEducationService.queryListByPid(tsm.getKey());
			 if (temp != null && temp.size() > 0) {
				 tsm.setChildren(temp);
				 loadAllChildren(temp);
			 }
		 }
	 }

	 /**
      * 获取子数据
      * @param inzEducation
      * @param req
      * @return
      */
	//@AutoLog(value = "教育阶段-获取子数据")
	@ApiOperation(value="教育阶段-获取子数据", notes="教育阶段-获取子数据")
	@GetMapping(value = "/childList")
	public Result<IPage<InzEducation>> queryPageList(InzEducation inzEducation,HttpServletRequest req) {
		QueryWrapper<InzEducation> queryWrapper = QueryGenerator.initQueryWrapper(inzEducation, req.getParameterMap());
		List<InzEducation> list = inzEducationService.list(queryWrapper);
		IPage<InzEducation> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
		return Result.OK(pageList);
	}

    /**
      * 批量查询子节点
      * @param parentIds 父ID（多个采用半角逗号分割）
      * @return 返回 IPage
      * @param parentIds
      * @return
      */
	//@AutoLog(value = "教育阶段-批量获取子数据")
    @ApiOperation(value="教育阶段-批量获取子数据", notes="教育阶段-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<InzEducation> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<InzEducation> list = inzEducationService.list(queryWrapper);
            IPage<InzEducation> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
	
	/**
	 *   添加
	 *
	 * @param inzEducation
	 * @return
	 */
	@AutoLog(value = "教育阶段-添加")
	@ApiOperation(value="教育阶段-添加", notes="教育阶段-添加")
    @RequiresPermissions("education:inz_education:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzEducation inzEducation) {
		inzEducationService.addInzEducation(inzEducation);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzEducation
	 * @return
	 */
	@AutoLog(value = "教育阶段-编辑")
	@ApiOperation(value="教育阶段-编辑", notes="教育阶段-编辑")
    @RequiresPermissions("education:inz_education:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzEducation inzEducation) {
		inzEducationService.updateInzEducation(inzEducation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "教育阶段-通过id删除")
	@ApiOperation(value="教育阶段-通过id删除", notes="教育阶段-通过id删除")
    @RequiresPermissions("education:inz_education:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzEducationService.deleteInzEducation(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "教育阶段-批量删除")
	@ApiOperation(value="教育阶段-批量删除", notes="教育阶段-批量删除")
    @RequiresPermissions("education:inz_education:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzEducationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "教育阶段-通过id查询")
	@ApiOperation(value="教育阶段-通过id查询", notes="教育阶段-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzEducation> queryById(@RequestParam(name="id",required=true) String id) {
		InzEducation inzEducation = inzEducationService.getById(id);
		if(inzEducation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzEducation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzEducation
    */
    @RequiresPermissions("education:inz_education:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzEducation inzEducation) {
		return super.exportXls(request, inzEducation, InzEducation.class, "教育阶段");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("education:inz_education:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, InzEducation.class);
    }

}
