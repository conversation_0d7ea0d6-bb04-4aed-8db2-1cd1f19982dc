package org.jeecg.modules.inz_words_articles.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_words_articles.entity.InzWordsArticles;
import org.jeecg.modules.inz_words_articles.service.IInzWordsArticlesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 短文表
 * @Author: jeecg-boot
 * @Date:   2025-07-06
 * @Version: V1.0
 */
@Api(tags="短文表")
@RestController
@RequestMapping("/inz_words_articles/inzWordsArticles")
@Slf4j
public class InzWordsArticlesController extends JeecgController<InzWordsArticles, IInzWordsArticlesService> {
	@Autowired
	private IInzWordsArticlesService inzWordsArticlesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzWordsArticles
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "短文表-分页列表查询")
	@ApiOperation(value="短文表-分页列表查询", notes="短文表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzWordsArticles>> queryPageList(InzWordsArticles inzWordsArticles,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzWordsArticles> queryWrapper = QueryGenerator.initQueryWrapper(inzWordsArticles, req.getParameterMap());
		Page<InzWordsArticles> page = new Page<InzWordsArticles>(pageNo, pageSize);
		IPage<InzWordsArticles> pageList = inzWordsArticlesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzWordsArticles
	 * @return
	 */
	@AutoLog(value = "短文表-添加")
	@ApiOperation(value="短文表-添加", notes="短文表-添加")
	@RequiresPermissions("inz_words_articles:inz_word_article:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzWordsArticles inzWordsArticles) {
		inzWordsArticlesService.save(inzWordsArticles);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzWordsArticles
	 * @return
	 */
	@AutoLog(value = "短文表-编辑")
	@ApiOperation(value="短文表-编辑", notes="短文表-编辑")
	@RequiresPermissions("inz_words_articles:inz_word_article:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzWordsArticles inzWordsArticles) {
		inzWordsArticlesService.updateById(inzWordsArticles);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "短文表-通过id删除")
	@ApiOperation(value="短文表-通过id删除", notes="短文表-通过id删除")
	@RequiresPermissions("inz_words_articles:inz_word_article:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzWordsArticlesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "短文表-批量删除")
	@ApiOperation(value="短文表-批量删除", notes="短文表-批量删除")
	@RequiresPermissions("inz_words_articles:inz_word_article:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzWordsArticlesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "短文表-通过id查询")
	@ApiOperation(value="短文表-通过id查询", notes="短文表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzWordsArticles> queryById(@RequestParam(name="id",required=true) String id) {
		InzWordsArticles inzWordsArticles = inzWordsArticlesService.getById(id);
		if(inzWordsArticles==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzWordsArticles);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzWordsArticles
    */
    @RequiresPermissions("inz_words_articles:inz_word_article:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzWordsArticles inzWordsArticles) {
        return super.exportXls(request, inzWordsArticles, InzWordsArticles.class, "短文表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("inz_words_articles:inz_word_article:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzWordsArticles.class);
    }

}
