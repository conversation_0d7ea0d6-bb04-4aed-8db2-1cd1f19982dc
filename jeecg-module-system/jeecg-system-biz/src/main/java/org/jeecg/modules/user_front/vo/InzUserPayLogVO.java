package org.jeecg.modules.user_front.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户金豆记录增强VO
 * @Author: Alex (工程师)
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@ApiModel(value="用户金豆记录增强VO", description="包含用户信息的金豆记录")
@Data
public class InzUserPayLogVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**记录ID*/
    @ApiModelProperty(value = "记录ID")
    private String id;

    /**用户ID*/
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**接受金币姓名*/
    @ApiModelProperty(value = "接受金币姓名")
    private String realName;

    /**接受金币手机号*/
    @ApiModelProperty(value = "接受金币手机号")
    private String phone;

    /**本次操作金币数量*/
    @ApiModelProperty(value = "本次操作金币数量")
    private Integer goldenBean;

    /**操作类型：1=增加，0=减少*/
    @ApiModelProperty(value = "操作类型：1=增加，0=减少")
    private Integer type;

    /**操作类型描述*/
    @ApiModelProperty(value = "操作类型描述")
    private String typeDesc;

    /**接受金币数量（增加的金币）*/
    @ApiModelProperty(value = "接受金币数量")
    private Integer receivedAmount;

    /**消耗金币数量（扣除的金币）*/
    @ApiModelProperty(value = "消耗金币数量")
    private Integer consumedAmount;

    /**操作前余额*/
    @ApiModelProperty(value = "操作前余额")
    private Integer beforeBalance;

    /**操作后余额*/
    @ApiModelProperty(value = "操作后余额")
    private Integer afterBalance;

    /**剩余金币数量（操作后余额的别名）*/
    @ApiModelProperty(value = "剩余金币数量")
    private Integer remainingBalance;

    /**操作描述*/
    @ApiModelProperty(value = "操作描述")
    private String content;

    /**接受金币时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接受金币时间")
    private Date createTime;

    /**操作人*/
    @ApiModelProperty(value = "操作人")
    private String createBy;

    /**来源用户ID*/
    @ApiModelProperty(value = "来源用户ID")
    private String sourceUserId;

    /**来源用户姓名*/
    @ApiModelProperty(value = "来源用户姓名")
    private String sourceUserName;

    /**来源用户手机号*/
    @ApiModelProperty(value = "来源用户手机号")
    private String sourceUserPhone;

    /**
     * 根据type和goldenBean计算接受和消耗金币数量
     */
    public void calculateAmounts() {
        if (this.type != null && this.goldenBean != null) {
            if (this.type == 1) {
                // 增加金币
                this.receivedAmount = Math.abs(this.goldenBean);
                this.consumedAmount = 0;
                this.typeDesc = "增加";
            } else {
                // 减少金币
                this.receivedAmount = 0;
                this.consumedAmount = Math.abs(this.goldenBean);
                this.typeDesc = "减少";
            }
        }
        
        // 设置剩余金币数量（使用afterBalance）
        if (this.afterBalance != null) {
            this.remainingBalance = this.afterBalance;
        }
    }

    /**
     * 从content中解析操作前后余额（兼容旧数据）
     */
    public void parseBalanceFromContent() {
        // 如果数据库字段有值，优先使用数据库字段
        if (this.beforeBalance != null && this.afterBalance != null) {
            this.remainingBalance = this.afterBalance;
            return;
        }
        
        // 兼容旧数据：从content中解析
        if (this.content != null && this.content.contains("原余额:") && this.content.contains("新余额:")) {
            try {
                // 解析格式：系统调整金豆 +100 (原余额:0 → 新余额:100)
                String[] parts = this.content.split("原余额:");
                if (parts.length > 1) {
                    String balancePart = parts[1];
                    String[] balanceParts = balancePart.split(" → 新余额:");
                    if (balanceParts.length > 1) {
                        if (this.beforeBalance == null) {
                            this.beforeBalance = Integer.parseInt(balanceParts[0].trim());
                        }
                        String afterPart = balanceParts[1].replace(")", "").trim();
                        if (this.afterBalance == null) {
                            this.afterBalance = Integer.parseInt(afterPart);
                        }
                        this.remainingBalance = this.afterBalance;
                    }
                }
            } catch (Exception e) {
                // 解析失败时，使用默认值
                this.remainingBalance = this.afterBalance;
            }
        }
    }
}
