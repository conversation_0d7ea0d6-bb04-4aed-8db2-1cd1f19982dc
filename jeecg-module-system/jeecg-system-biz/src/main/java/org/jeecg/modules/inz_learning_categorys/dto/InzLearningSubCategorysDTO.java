package org.jeecg.modules.inz_learning_categorys.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Description: 添加子分类DTO
 * @Author: Alex (工程师)
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "InzLearningSubCategorysDTO", description = "添加子分类数据传输对象")
public class InzLearningSubCategorysDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父分类ID", required = true)
    @NotBlank(message = "父分类ID不能为空")
    private String categoryId;

    @ApiModelProperty(value = "子分类名称", required = true)
    @NotBlank(message = "子分类名称不能为空")
    @Size(max = 100, message = "子分类名称长度不能超过100个字符")
    private String subCategoryName;

    @ApiModelProperty(value = "分类描述")
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;

    @ApiModelProperty(value = "封面图片URL")
    private String coverImage;
}
