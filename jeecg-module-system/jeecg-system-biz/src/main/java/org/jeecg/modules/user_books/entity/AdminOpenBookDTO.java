package org.jeecg.modules.user_books.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 管理员开通单词书权限
 * @Author: jeecg-boot
 * @Date:   2025-07-10
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="创总等开通单词书权限", description="创总等开通单词书权限")
public class AdminOpenBookDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限类型：1-体验权限，2-全年权限", required = true)
    private Integer permissionType;

    @ApiModelProperty(value = "单词书ID", required = true)
    @NotBlank(message = "单词书ID不能为空")
    private String bookId;

    @ApiModelProperty(value = "用户ID列表", required = true)
    @NotEmpty(message = "用户ID列表不能为空")
    private List<String> userIds;
} 