package org.jeecg.modules.inz_learning_categorys.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_learning_categorys.dto.InzLearningSubCategorysDTO;
import org.jeecg.modules.inz_learning_categorys.entity.InzLearningCategorys;
import org.jeecg.modules.inz_learning_categorys.service.IInzLearningCategorysService;
import org.jeecg.modules.inz_learning_modules.entity.InzLearningModules;
import org.jeecg.modules.inz_learning_modules.service.IInzLearningModulesService;
import org.jeecg.modules.inz_learning_videos.entity.InzLearningVideos;
import org.jeecg.modules.inz_learning_videos.service.IInzLearningVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 继续深造-分类
 * @Author: jeecg-boot
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Api(tags = "继续深造-分类")
@RestController
@RequestMapping("/inz_learning_categorys/inzLearningCategorys")
@Slf4j
public class InzLearningCategorysController extends JeecgController<InzLearningCategorys, IInzLearningCategorysService> {
    @Autowired
    private IInzLearningCategorysService inzLearningCategorysService;

    @Autowired
    private IInzLearningVideosService inzLearningVideosService;

    @Autowired
    private IInzLearningModulesService inzLearningModulesService;

    /**
     * 分页列表查询
     *
     * @param inzLearningCategorys
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "继续深造-分类-分页列表查询")
    @ApiOperation(value = "继续深造-分类-分页列表查询", notes = "继续深造-分类-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzLearningCategorys>> queryPageList(InzLearningCategorys inzLearningCategorys,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        QueryWrapper<InzLearningCategorys> queryWrapper = QueryGenerator.initQueryWrapper(inzLearningCategorys, req.getParameterMap());
        queryWrapper.eq("level",1)
                .orderByAsc("sort_order");
        Page<InzLearningCategorys> page = new Page<InzLearningCategorys>(pageNo, pageSize);
        IPage<InzLearningCategorys> pageList = inzLearningCategorysService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzLearningCategorys
     * @return
     */
    @AutoLog(value = "继续深造-分类-添加")
    @ApiOperation(value = "继续深造-分类-添加", notes = "继续深造-分类-添加")
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzLearningCategorys inzLearningCategorys) {
        try {
            log.info("开始添加一级分类 - 模块ID: {}, 分类名称: {}",
                    inzLearningCategorys.getModuleId(), inzLearningCategorys.getCategoryName());

            // 1. 验证必填字段
            if (inzLearningCategorys.getModuleId() == null || inzLearningCategorys.getModuleId().trim().isEmpty()) {
                return Result.error("模块ID不能为空");
            }

            if (inzLearningCategorys.getCategoryName() == null || inzLearningCategorys.getCategoryName().trim().isEmpty()) {
                return Result.error("分类名称不能为空");
            }

            // 2. 验证模块是否存在
            // 这里可以添加模块存在性验证，如果需要的话

            // 3. 检查同一模块下是否已存在同名的一级分类
            QueryWrapper<InzLearningCategorys> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("module_id", inzLearningCategorys.getModuleId())
                       .eq("category_name", inzLearningCategorys.getCategoryName().trim())
                       .eq("level", 1)
                       .eq("parent_id", "0");
            long existCount = inzLearningCategorysService.count(checkWrapper);
            if (existCount > 0) {
                return Result.error("该模块下已存在同名的一级分类");
            }

            // 4. 获取当前模块下一级分类的最大排序号
            QueryWrapper<InzLearningCategorys> sortWrapper = new QueryWrapper<>();
            sortWrapper.eq("module_id", inzLearningCategorys.getModuleId())
                      .eq("level", 1)
                      .eq("parent_id", "0")
                      .orderByDesc("sort_order")
                      .last("LIMIT 1");
            InzLearningCategorys lastCategory = inzLearningCategorysService.getOne(sortWrapper);
            int nextSortOrder = (lastCategory != null && lastCategory.getSortOrder() != null)
                              ? lastCategory.getSortOrder() + 1 : 1;

            // 5. 生成分类编码
            String categoryCode = generateCategoryCode(inzLearningCategorys.getCategoryName());

            // 6. 设置一级分类的固定字段
            inzLearningCategorys.setLevel(1);                                    // 一级分类
            inzLearningCategorys.setParentId("0");                              // 顶级分类
            inzLearningCategorys.setCategoryCode(categoryCode);                 // 分类编码
            inzLearningCategorys.setSortOrder(nextSortOrder);                   // 排序号
            inzLearningCategorys.setStatus(1);                                  // 默认启用
            inzLearningCategorys.setTotalVideos(0);                            // 初始视频数量为0
            inzLearningCategorys.setCreateTime(new Date());
            inzLearningCategorys.setUpdateTime(new Date());

            // 7. 保存分类
            boolean saveResult = inzLearningCategorysService.save(inzLearningCategorys);
            if (!saveResult) {
                return Result.error("保存分类失败，请重试");
            }

            log.info("一级分类添加成功 - ID: {}, 名称: {}, 模块ID: {}, 排序号: {}",
                    inzLearningCategorys.getId(), inzLearningCategorys.getCategoryName(),
                    inzLearningCategorys.getModuleId(), nextSortOrder);

            return Result.OK("添加成功！分类ID: " + inzLearningCategorys.getId());

        } catch (Exception e) {
            log.error("添加一级分类失败 - 模块ID: {}, 分类名称: {}",
                    inzLearningCategorys.getModuleId(), inzLearningCategorys.getCategoryName(), e);
            return Result.error("添加分类失败: " + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param inzLearningCategorys
     * @return
     */
    @AutoLog(value = "继续深造-分类-编辑")
    @ApiOperation(value = "继续深造-分类-编辑", notes = "继续深造-分类-编辑")
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningCategorys inzLearningCategorys) {
        inzLearningCategorysService.updateById(inzLearningCategorys);
        return Result.OK("编辑成功!");
    }

    @ApiOperation(value = "课程分类-获取子分类", notes = "根据父分类ID获取子分类列表")
    @GetMapping(value = "/sub-categories")
    public Result<List<InzLearningCategorys>> getSubCategories(
            @ApiParam(value = "父分类ID", required = true) @RequestParam("parentId") String parentId) {
        log.info("查询子分类 - 父分类ID: {}", parentId);
        List<InzLearningCategorys> subCategories = inzLearningCategorysService.getSubCategories(parentId);
        log.info("查询结果 - 父分类ID: {}, 子分类数量: {}", parentId, subCategories.size());
        return Result.OK(subCategories);
    }

    /**
     * 通过id删除 - 支持级联删除子分类
     *
     * @param id
     * @return
     */
    @AutoLog(value = "继续深造-分类-通过id删除")
    @ApiOperation(value = "继续深造-分类-通过id删除", notes = "继续深造-分类-通过id删除，删除父分类时会级联删除所有子分类")
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            log.info("开始删除分类 - 分类ID: {}", id);

            // 1. 验证分类是否存在
            InzLearningCategorys category = inzLearningCategorysService.getById(id);
            if (category == null) {
                return Result.error("分类不存在，请检查分类ID");
            }

            // 2. 删除关联的视频
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", id);
            List<InzLearningVideos> relatedVideos = inzLearningVideosService.list(videoWrapper);

            if (!relatedVideos.isEmpty()) {
                log.info("发现分类下有 {} 个关联视频，开始删除 - 分类ID: {}", relatedVideos.size(), id);

                // 删除所有关联视频
                boolean videoDeleteResult = inzLearningVideosService.remove(videoWrapper);
                if (!videoDeleteResult) {
                    return Result.error("删除分类下的关联视频失败，请重试");
                }

                // 更新分类和模块的视频统计
                updateVideoStatisticsAfterDelete(relatedVideos);

                log.info("成功删除分类下的 {} 个关联视频 - 分类ID: {}", relatedVideos.size(), id);
            }

            // 3. 如果是父分类，检查并级联删除所有子分类
            if (category.getLevel() != null && category.getLevel() == 1) {
                // 这是一级分类，需要级联删除子分类
                Result<String> cascadeResult = cascadeDeleteSubCategories(id);
                if (!cascadeResult.isSuccess()) {
                    return cascadeResult; // 如果级联删除失败，返回错误信息
                }
            }

            // 4. 删除当前分类
            boolean deleteResult = inzLearningCategorysService.removeById(id);
            if (!deleteResult) {
                return Result.error("删除分类失败，请重试");
            }

            log.info("分类删除成功 - ID: {}, 名称: {}, 层级: {}",
                    id, category.getCategoryName(), category.getLevel());

            return Result.OK("删除成功!");

        } catch (Exception e) {
            log.error("删除分类失败 - 分类ID: {}", id, e);
            return Result.error("删除分类失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "添加子分类", notes = "为指定父分类添加子分类")
    @PostMapping(value = "/add-sub-category")
    public Result<String> addSubCategory(@RequestBody InzLearningSubCategorysDTO dto) {
        try {
            log.info("开始添加子分类 - 父分类ID: {}, 子分类名称: {}", dto.getCategoryId(), dto.getSubCategoryName());

            // 1. 验证父分类是否存在
            InzLearningCategorys parentCategory = inzLearningCategorysService.getById(dto.getCategoryId());
            if (parentCategory == null) {
                return Result.error("父分类不存在，请检查分类ID");
            }

            // 2. 检查子分类名称是否重复
            QueryWrapper<InzLearningCategorys> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("parent_id", dto.getCategoryId())
                       .eq("category_name", dto.getSubCategoryName());
            long existCount = inzLearningCategorysService.count(checkWrapper);
            if (existCount > 0) {
                return Result.error("该父分类下已存在同名的子分类");
            }

            // 3. 获取当前父分类下子分类的最大排序号
            QueryWrapper<InzLearningCategorys> sortWrapper = new QueryWrapper<>();
            sortWrapper.eq("parent_id", dto.getCategoryId())
                      .orderByDesc("sort_order")
                      .last("LIMIT 1");
            InzLearningCategorys lastSubCategory = inzLearningCategorysService.getOne(sortWrapper);
            int nextSortOrder = (lastSubCategory != null && lastSubCategory.getSortOrder() != null)
                              ? lastSubCategory.getSortOrder() + 1 : 1;

            // 4. 创建子分类
            InzLearningCategorys subCategory = new InzLearningCategorys();
            subCategory.setCategoryName(dto.getSubCategoryName());
            subCategory.setCategoryCode(generateCategoryCode(dto.getSubCategoryName())); // 生成分类编码
            subCategory.setParentId(dto.getCategoryId());
            subCategory.setModuleId(parentCategory.getModuleId()); // 继承父分类的模块ID
            subCategory.setLevel(parentCategory.getLevel() + 1); // 父分类层级+1
            subCategory.setSortOrder(nextSortOrder); // 设置排序号
            subCategory.setStatus(1); // 默认启用状态
            subCategory.setTotalVideos(0); // 初始视频数量为0
            subCategory.setDescription(dto.getDescription()); // 设置描述
            subCategory.setCreateTime(new Date());
            subCategory.setUpdateTime(new Date());

            // 5. 保存子分类
            boolean saveResult = inzLearningCategorysService.save(subCategory);
            if (!saveResult) {
                return Result.error("保存子分类失败，请重试");
            }

            log.info("子分类添加成功 - ID: {}, 名称: {}, 父分类: {}",
                    subCategory.getId(), subCategory.getCategoryName(), dto.getCategoryId());

            return Result.OK("添加成功！子分类名称: " + subCategory.getCategoryName());

        } catch (Exception e) {
            log.error("添加子分类失败 - 父分类ID: {}, 子分类名称: {}", dto.getCategoryId(), dto.getSubCategoryName(), e);
            return Result.error("添加子分类失败: " + e.getMessage());
        }
    }


    /**
     * 批量删除 - 支持级联删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "继续深造-分类-批量删除")
    @ApiOperation(value = "继续深造-分类-批量删除", notes = "继续深造-分类-批量删除，删除父分类时会级联删除所有子分类")
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            if (ids == null || ids.trim().isEmpty()) {
                return Result.error("请选择要删除的分类");
            }

            String[] idArray = ids.split(",");
            log.info("开始批量删除分类 - 分类数量: {}, IDs: {}", idArray.length, ids);

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (String categoryId : idArray) {
                categoryId = categoryId.trim();
                if (categoryId.isEmpty()) {
                    continue;
                }

                try {
                    // 调用单个删除方法，利用其级联删除逻辑
                    Result<String> deleteResult = delete(categoryId);
                    if (deleteResult.isSuccess()) {
                        successCount++;
                        log.info("批量删除成功 - ID: {}", categoryId);
                    } else {
                        failCount++;
                        errorMessages.append("ID ").append(categoryId).append(": ").append(deleteResult.getMessage()).append("；");
                    }

                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("ID ").append(categoryId).append(": 删除异常；");
                    log.error("批量删除单个分类失败 - ID: {}", categoryId, e);
                }
            }

            String resultMessage = String.format("批量删除完成！成功: %d 个，失败: %d 个", successCount, failCount);
            if (failCount > 0) {
                resultMessage += "。失败原因：" + errorMessages.toString();
            }

            log.info("批量删除分类完成 - 成功: {}, 失败: {}", successCount, failCount);

            if (successCount > 0) {
                return Result.OK(resultMessage);
            } else {
                return Result.error(resultMessage);
            }

        } catch (Exception e) {
            log.error("批量删除分类失败 - IDs: {}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "继续深造-分类-通过id查询")
    @ApiOperation(value = "继续深造-分类-通过id查询", notes = "继续深造-分类-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzLearningCategorys> queryById(@RequestParam(name = "id", required = true) String id) {
        InzLearningCategorys inzLearningCategorys = inzLearningCategorysService.getById(id);
        if (inzLearningCategorys == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzLearningCategorys);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzLearningCategorys
     */
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzLearningCategorys inzLearningCategorys) {
        return super.exportXls(request, inzLearningCategorys, InzLearningCategorys.class, "继续深造-分类");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_learning_categorys:inz_learning_category:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzLearningCategorys.class);
    }

    /**
     * 删除子分类接口
     */
    @ApiOperation(value = "删除子分类", notes = "删除指定的子分类，会检查是否有关联的视频")
    @DeleteMapping(value = "/delete-sub-category/{categoryId}")
    public Result<String> deleteSubCategory(@PathVariable("categoryId") String categoryId) {
        try {
            log.info("开始删除子分类 - 分类ID: {}", categoryId);

            // 1. 验证分类是否存在
            InzLearningCategorys category = inzLearningCategorysService.getById(categoryId);
            if (category == null) {
                return Result.error("分类不存在，请检查分类ID");
            }

            // 2. 验证是否为子分类（level > 1）
            if (category.getLevel() == null || category.getLevel() <= 1) {
                return Result.error("只能删除子分类，不能删除一级分类");
            }

            // 3. 删除关联的视频
            QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
            videoWrapper.eq("category_id", categoryId);
            List<InzLearningVideos> relatedVideos = inzLearningVideosService.list(videoWrapper);

            if (!relatedVideos.isEmpty()) {
                log.info("发现子分类下有 {} 个关联视频，开始删除 - 分类ID: {}", relatedVideos.size(), categoryId);

                // 删除所有关联视频
                boolean videoDeleteResult = inzLearningVideosService.remove(videoWrapper);
                if (!videoDeleteResult) {
                    return Result.error("删除分类下的关联视频失败，请重试");
                }

                // 更新分类和模块的视频统计
                updateVideoStatisticsAfterDelete(relatedVideos);

                log.info("成功删除子分类下的 {} 个关联视频 - 分类ID: {}", relatedVideos.size(), categoryId);
            }

            // 4. 检查是否有下级子分类
            QueryWrapper<InzLearningCategorys> subCategoryWrapper = new QueryWrapper<>();
            subCategoryWrapper.eq("parent_id", categoryId);
            long subCategoryCount = inzLearningCategorysService.count(subCategoryWrapper);

            if (subCategoryCount > 0) {
                return Result.error("该分类下还有 " + subCategoryCount + " 个子分类，请先删除这些子分类");
            }

            // 5. 记录删除前的信息
            String parentId = category.getParentId();
            String categoryName = category.getCategoryName();
            String moduleId = category.getModuleId();

            // 6. 执行删除
            boolean deleteResult = inzLearningCategorysService.removeById(categoryId);
            if (!deleteResult) {
                return Result.error("删除分类失败，请重试");
            }

            // 7. 更新父分类的子分类数量（如果需要的话）
            if (parentId != null && !parentId.equals("0")) {
                updateParentCategoryCount(parentId);
            }

            log.info("子分类删除成功 - ID: {}, 名称: {}, 模块ID: {}, 父分类ID: {}",
                    categoryId, categoryName, moduleId, parentId);

            return Result.OK("删除成功！");

        } catch (Exception e) {
            log.error("删除子分类失败 - 分类ID: {}", categoryId, e);
            return Result.error("删除分类失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除子分类接口
     */
    @ApiOperation(value = "批量删除子分类", notes = "批量删除多个子分类，会检查每个分类是否有关联数据")
    @DeleteMapping(value = "/delete-sub-categories")
    public Result<String> deleteSubCategories(@RequestParam("categoryIds") String categoryIds) {
        try {
            if (categoryIds == null || categoryIds.trim().isEmpty()) {
                return Result.error("请选择要删除的分类");
            }

            String[] idArray = categoryIds.split(",");
            log.info("开始批量删除子分类 - 分类数量: {}, IDs: {}", idArray.length, categoryIds);

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (String categoryId : idArray) {
                categoryId = categoryId.trim();
                if (categoryId.isEmpty()) {
                    continue;
                }

                try {
                    // 验证分类是否存在
                    InzLearningCategorys category = inzLearningCategorysService.getById(categoryId);
                    if (category == null) {
                        failCount++;
                        errorMessages.append("分类ID ").append(categoryId).append(" 不存在；");
                        continue;
                    }

                    // 验证是否为子分类
                    if (category.getLevel() == null || category.getLevel() <= 1) {
                        failCount++;
                        errorMessages.append("分类 ").append(category.getCategoryName()).append(" 不是子分类；");
                        continue;
                    }

                    // 删除关联的视频
                    QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
                    videoWrapper.eq("category_id", categoryId);
                    List<InzLearningVideos> relatedVideos = inzLearningVideosService.list(videoWrapper);

                    if (!relatedVideos.isEmpty()) {
                        log.info("发现分类下有 {} 个关联视频，开始删除 - 分类ID: {}, 名称: {}",
                                relatedVideos.size(), categoryId, category.getCategoryName());

                        // 删除关联视频
                        boolean videoDeleteResult = inzLearningVideosService.remove(videoWrapper);
                        if (!videoDeleteResult) {
                            failCount++;
                            errorMessages.append("分类 ").append(category.getCategoryName()).append(" 下的视频删除失败；");
                            continue;
                        }

                        // 更新分类和模块的视频统计
                        updateVideoStatisticsAfterDelete(relatedVideos);

                        log.info("成功删除分类下的 {} 个关联视频 - 分类ID: {}, 名称: {}",
                                relatedVideos.size(), categoryId, category.getCategoryName());
                    }

                    QueryWrapper<InzLearningCategorys> subWrapper = new QueryWrapper<>();
                    subWrapper.eq("parent_id", categoryId);
                    long subCount = inzLearningCategorysService.count(subWrapper);

                    if (subCount > 0) {
                        failCount++;
                        errorMessages.append("分类 ").append(category.getCategoryName()).append(" 下有子分类；");
                        continue;
                    }

                    // 执行删除
                    boolean deleteResult = inzLearningCategorysService.removeById(categoryId);
                    if (deleteResult) {
                        successCount++;
                        log.info("批量删除成功 - ID: {}, 名称: {}", categoryId, category.getCategoryName());
                    } else {
                        failCount++;
                        errorMessages.append("分类 ").append(category.getCategoryName()).append(" 删除失败；");
                    }

                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("分类ID ").append(categoryId).append(" 删除异常；");
                    log.error("批量删除单个分类失败 - ID: {}", categoryId, e);
                }
            }

            String resultMessage = String.format("批量删除完成！成功: %d 个，失败: %d 个", successCount, failCount);
            if (failCount > 0) {
                resultMessage += "。失败原因：" + errorMessages.toString();
            }

            log.info("批量删除子分类完成 - 成功: {}, 失败: {}", successCount, failCount);

            if (successCount > 0) {
                return Result.OK(resultMessage);
            } else {
                return Result.error(resultMessage);
            }

        } catch (Exception e) {
            log.error("批量删除子分类失败 - IDs: {}", categoryIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 生成分类编码
     */
    private String generateCategoryCode(String categoryName) {
        // 简单的编码生成逻辑：分类名称拼音首字母 + 时间戳后6位
        return "CAT_" + System.currentTimeMillis() % 1000000;
    }

    /**
     * 级联删除子分类
     * @param parentId 父分类ID
     * @return 删除结果
     */
    private Result<String> cascadeDeleteSubCategories(String parentId) {
        try {
            log.info("开始级联删除子分类 - 父分类ID: {}", parentId);

            // 1. 查找所有子分类
            QueryWrapper<InzLearningCategorys> subCategoryWrapper = new QueryWrapper<>();
            subCategoryWrapper.eq("parent_id", parentId);
            List<InzLearningCategorys> subCategories = inzLearningCategorysService.list(subCategoryWrapper);

            if (subCategories.isEmpty()) {
                log.info("父分类下没有子分类 - 父分类ID: {}", parentId);
                return Result.OK("没有子分类需要删除");
            }

            int deletedCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            // 2. 逐个检查并删除子分类
            for (InzLearningCategorys subCategory : subCategories) {
                try {
                    // 删除子分类下的关联视频
                    QueryWrapper<InzLearningVideos> videoWrapper = new QueryWrapper<>();
                    videoWrapper.eq("category_id", subCategory.getId());
                    List<InzLearningVideos> subCategoryVideos = inzLearningVideosService.list(videoWrapper);

                    if (!subCategoryVideos.isEmpty()) {
                        log.info("发现子分类下有 {} 个关联视频，开始删除 - 子分类ID: {}, 名称: {}",
                                subCategoryVideos.size(), subCategory.getId(), subCategory.getCategoryName());

                        // 删除子分类下的所有视频
                        boolean videoDeleteResult = inzLearningVideosService.remove(videoWrapper);
                        if (!videoDeleteResult) {
                            errorMessages.append("子分类 '").append(subCategory.getCategoryName())
                                       .append("' 下的视频删除失败；");
                            continue;
                        }

                        // 更新分类和模块的视频统计
                        updateVideoStatisticsAfterDelete(subCategoryVideos);

                        log.info("成功删除子分类下的 {} 个关联视频 - 子分类ID: {}, 名称: {}",
                                subCategoryVideos.size(), subCategory.getId(), subCategory.getCategoryName());
                    }

                    // 检查是否有更深层的子分类（如果支持多级分类）
                    QueryWrapper<InzLearningCategorys> deepSubWrapper = new QueryWrapper<>();
                    deepSubWrapper.eq("parent_id", subCategory.getId());
                    long deepSubCount = inzLearningCategorysService.count(deepSubWrapper);

                    if (deepSubCount > 0) {
                        errorMessages.append("子分类 '").append(subCategory.getCategoryName())
                                   .append("' 下有 ").append(deepSubCount).append(" 个下级分类；");
                        continue;
                    }

                    // 删除子分类
                    boolean deleteResult = inzLearningCategorysService.removeById(subCategory.getId());
                    if (deleteResult) {
                        deletedCount++;
                        log.info("级联删除子分类成功 - ID: {}, 名称: {}",
                                subCategory.getId(), subCategory.getCategoryName());
                    } else {
                        errorMessages.append("子分类 '").append(subCategory.getCategoryName()).append("' 删除失败；");
                    }

                } catch (Exception e) {
                    log.error("级联删除单个子分类失败 - ID: {}, 名称: {}",
                            subCategory.getId(), subCategory.getCategoryName(), e);
                    errorMessages.append("子分类 '").append(subCategory.getCategoryName()).append("' 删除异常；");
                }
            }

            // 3. 检查删除结果
            if (errorMessages.length() > 0) {
                String errorMsg = String.format("无法删除父分类，因为存在以下问题：%s", errorMessages.toString());
                log.warn("级联删除部分失败 - 父分类ID: {}, 成功删除: {}, 错误: {}",
                        parentId, deletedCount, errorMessages.toString());
                return Result.error(errorMsg);
            }

            log.info("级联删除子分类完成 - 父分类ID: {}, 删除数量: {}", parentId, deletedCount);
            return Result.OK("成功级联删除 " + deletedCount + " 个子分类");

        } catch (Exception e) {
            log.error("级联删除子分类失败 - 父分类ID: {}", parentId, e);
            return Result.error("级联删除子分类失败: " + e.getMessage());
        }
    }

    /**
     * 删除视频后更新统计信息
     * @param deletedVideos 被删除的视频列表
     */
    private void updateVideoStatisticsAfterDelete(List<InzLearningVideos> deletedVideos) {
        try {
            if (deletedVideos == null || deletedVideos.isEmpty()) {
                return;
            }

            log.info("开始更新视频删除后的统计信息 - 删除视频数量: {}", deletedVideos.size());

            // 按分类ID分组统计
            Map<String, Long> categoryVideoCount = deletedVideos.stream()
                    .collect(Collectors.groupingBy(InzLearningVideos::getCategoryId, Collectors.counting()));

            // 按模块ID分组统计
            Map<String, Long> moduleVideoCount = new HashMap<>();

            for (Map.Entry<String, Long> entry : categoryVideoCount.entrySet()) {
                String categoryId = entry.getKey();
                Long videoCount = entry.getValue();

                // 更新分类的视频统计
                updateCategoryVideoCount(categoryId, -videoCount.intValue());

                // 获取分类信息以更新模块统计
                InzLearningCategorys category = inzLearningCategorysService.getById(categoryId);
                if (category != null && category.getModuleId() != null) {
                    moduleVideoCount.merge(category.getModuleId(), videoCount, Long::sum);
                }
            }

            // 更新模块的视频统计
            for (Map.Entry<String, Long> entry : moduleVideoCount.entrySet()) {
                String moduleId = entry.getKey();
                Long videoCount = entry.getValue();
                updateModuleVideoCount(moduleId, -videoCount.intValue());
            }

            log.info("视频删除后统计信息更新完成 - 影响分类: {}, 影响模块: {}",
                    categoryVideoCount.size(), moduleVideoCount.size());

        } catch (Exception e) {
            log.error("更新视频删除后的统计信息失败", e);
        }
    }

    /**
     * 更新分类的视频数量统计
     * @param categoryId 分类ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateCategoryVideoCount(String categoryId, int deltaCount) {
        try {
            InzLearningCategorys category = inzLearningCategorysService.getById(categoryId);
            if (category != null) {
                int currentCount = category.getTotalVideos() != null ? category.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                category.setTotalVideos(newCount);
                category.setUpdateTime(new Date());
                inzLearningCategorysService.updateById(category);

                log.info("更新分类视频统计 - 分类ID: {}, 原数量: {}, 变化: {}, 新数量: {}",
                        categoryId, currentCount, deltaCount, newCount);

                // 如果是子分类，还需要更新父分类的统计
                if (category.getParentId() != null && !"0".equals(category.getParentId())) {
                    updateCategoryVideoCount(category.getParentId(), deltaCount);
                }
            }
        } catch (Exception e) {
            log.error("更新分类视频统计失败 - 分类ID: {}, 变化数量: {}", categoryId, deltaCount, e);
        }
    }

    /**
     * 更新模块的视频数量统计
     * @param moduleId 模块ID
     * @param deltaCount 变化数量（正数增加，负数减少）
     */
    private void updateModuleVideoCount(String moduleId, int deltaCount) {
        try {
            InzLearningModules module = inzLearningModulesService.getById(moduleId);
            if (module != null) {
                int currentCount = module.getTotalVideos() != null ? module.getTotalVideos() : 0;
                int newCount = Math.max(0, currentCount + deltaCount); // 确保不会小于0

                module.setTotalVideos(newCount);
                module.setUpdateTime(new Date());
                inzLearningModulesService.updateById(module);

                log.info("更新模块视频统计 - 模块ID: {}, 原数量: {}, 变化: {}, 新数量: {}",
                        moduleId, currentCount, deltaCount, newCount);
            }
        } catch (Exception e) {
            log.error("更新模块视频统计失败 - 模块ID: {}, 变化数量: {}", moduleId, deltaCount, e);
        }
    }

    /**
     * 更新父分类的统计信息
     */
    private void updateParentCategoryCount(String parentId) {
        try {
            QueryWrapper<InzLearningCategorys> wrapper = new QueryWrapper<>();
            wrapper.eq("parent_id", parentId).eq("status", 1);
            long subCategoryCount = inzLearningCategorysService.count(wrapper);

            InzLearningCategorys parentCategory = inzLearningCategorysService.getById(parentId);
            if (parentCategory != null) {
                // 这里可以添加更新父分类统计信息的逻辑
                parentCategory.setUpdateTime(new Date());
                inzLearningCategorysService.updateById(parentCategory);
                log.info("更新父分类统计信息 - 父分类ID: {}, 子分类数量: {}", parentId, subCategoryCount);
            }
        } catch (Exception e) {
            log.error("更新父分类统计信息失败 - 父分类ID: {}", parentId, e);
        }
    }

}
