package org.jeecg.modules.inz_learning_modules.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Description: 模块添加DTO
 * @Author: Alex (工程师)
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "ModuleAddDTO", description = "模块添加数据传输对象")
public class ModuleAddDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模块名称", required = true)
    @NotBlank(message = "模块名称不能为空")
    @Size(max = 100, message = "模块名称长度不能超过100个字符")
    private String moduleName;

    @ApiModelProperty(value = "模块描述")
    @Size(max = 500, message = "模块描述长度不能超过500个字符")
    private String moduleDesc;
}
