package org.jeecg.modules.phonetic_symbols.service.impl;

import org.jeecg.modules.phonetic_symbols.entity.InzPhoneticSymbols;
import org.jeecg.modules.phonetic_symbols.mapper.InzPhoneticSymbolsMapper;
import org.jeecg.modules.phonetic_symbols.service.IInzPhoneticSymbolsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 音标管理
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Service
public class InzPhoneticSymbolsServiceImpl extends ServiceImpl<InzPhoneticSymbolsMapper, InzPhoneticSymbols> implements IInzPhoneticSymbolsService {

}
