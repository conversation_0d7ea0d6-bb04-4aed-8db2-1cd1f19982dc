package org.jeecg.modules.inz_learning_modules.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.inz_learning_modules.dto.InzLearningModulesDTO;
import org.jeecg.modules.inz_learning_modules.dto.ModuleAddDTO;
import org.jeecg.modules.inz_learning_modules.entity.InzLearningModules;
import org.jeecg.modules.inz_learning_modules.service.IInzLearningModulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 继续深造-模块
 * @Author: jeecg-boot
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Api(tags = "继续深造-模块")
@RestController
@RequestMapping("/inz_learning_modules/inzLearningModules")
@Slf4j
public class InzLearningModulesController extends JeecgController<InzLearningModules, IInzLearningModulesService> {
    @Autowired
    private IInzLearningModulesService inzLearningModulesService;

    /**
     * 分页列表查询
     *
     * @param inzLearningModules
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "继续深造-模块-分页列表查询")
    @ApiOperation(value = "继续深造-模块-分页列表查询", notes = "继续深造-模块-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzLearningModules>> queryPageList(InzLearningModules inzLearningModules,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                           HttpServletRequest req) {
        QueryWrapper<InzLearningModules> queryWrapper = QueryGenerator.initQueryWrapper(inzLearningModules, req.getParameterMap());
        queryWrapper.orderByAsc("sort_order");
        Page<InzLearningModules> page = new Page<InzLearningModules>(pageNo, pageSize);
        IPage<InzLearningModules> pageList = inzLearningModulesService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加模块（仅需传入模块名称）
     *
     * @param moduleAddDTO 模块添加参数
     * @return
     */
    @AutoLog(value = "继续深造-模块-添加")
    @ApiOperation(value = "继续深造-模块-添加", notes = "根据模块名称添加模块，自动生成其他字段")
    @RequiresPermissions("inz_learning_modules:inz_learning_module:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ModuleAddDTO moduleAddDTO) {
        try {
            log.info("开始添加模块 - 模块名称: {}", moduleAddDTO.getModuleName());

            // 1. 验证必填字段
            if (moduleAddDTO.getModuleName() == null || moduleAddDTO.getModuleName().trim().isEmpty()) {
                return Result.error("模块名称不能为空");
            }

            String moduleName = moduleAddDTO.getModuleName().trim();

            // 2. 检查模块名称是否重复
            QueryWrapper<InzLearningModules> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("module_name", moduleName);
            long existCount = inzLearningModulesService.count(checkWrapper);

            if (existCount > 0) {
                return Result.error("模块名称 '" + moduleName + "' 已存在，请使用其他名称");
            }

            // 3. 生成模块编码
            String moduleCode = generateModuleCode(moduleName);

            // 4. 获取排序号
            Integer sortOrder = getNextModuleSortOrder();

            // 5. 创建模块记录
            InzLearningModules module = new InzLearningModules();
            module.setModuleName(moduleName);
            module.setModuleCode(moduleCode);
            module.setSortOrder(sortOrder);
            module.setStatus(1); // 默认启用
            module.setTotalVideos(0); // 初始视频数量为0
            module.setCreateTime(new Date());
            module.setUpdateTime(new Date());

            // 6. 保存模块
            boolean saveResult = inzLearningModulesService.save(module);
            if (!saveResult) {
                return Result.error("保存模块失败，请重试");
            }

            log.info("模块添加成功 - ID: {}, 名称: {}, 编码: {}, 排序号: {}",
                    module.getId(), module.getModuleName(), module.getModuleCode(), sortOrder);

            return Result.OK("添加成功！模块ID: " + module.getId());

        } catch (Exception e) {
            log.error("添加模块失败 - 模块名称: {}", moduleAddDTO.getModuleName(), e);
            return Result.error("添加模块失败: " + e.getMessage());
        }
    }

    /**
     * 生成模块编码
     */
    private String generateModuleCode(String moduleName) {
        // 生成模块编码：MOD_ + 时间戳后6位
        return "MOD_" + System.currentTimeMillis() % 1000000;
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextModuleSortOrder() {
        try {
            QueryWrapper<InzLearningModules> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("sort_order").last("LIMIT 1");
            InzLearningModules lastModule = inzLearningModulesService.getOne(queryWrapper);
            return (lastModule != null && lastModule.getSortOrder() != null)
                   ? lastModule.getSortOrder() + 1 : 1;
        } catch (Exception e) {
            log.error("获取模块排序号失败", e);
            return 1;
        }
    }

    /**
     * 编辑
     *
     * @param inzLearningModules
     * @return
     */
    @AutoLog(value = "继续深造-模块-编辑")
    @ApiOperation(value = "继续深造-模块-编辑", notes = "继续深造-模块-编辑")
    @RequiresPermissions("inz_learning_modules:inz_learning_module:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningModules inzLearningModules) {
        inzLearningModulesService.updateById(inzLearningModules);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "继续深造-模块-通过id删除")
    @ApiOperation(value = "继续深造-模块-通过id删除", notes = "继续深造-模块-通过id删除")
    @RequiresPermissions("inz_learning_modules:inz_learning_module:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzLearningModulesService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "继续深造-模块-批量删除")
    @ApiOperation(value = "继续深造-模块-批量删除", notes = "继续深造-模块-批量删除")
    @RequiresPermissions("inz_learning_modules:inz_learning_module:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzLearningModulesService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "继续深造-模块-通过id查询")
    @ApiOperation(value = "继续深造-模块-通过id查询", notes = "继续深造-模块-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzLearningModules> queryById(@RequestParam(name = "id", required = true) String id) {
        InzLearningModules inzLearningModules = inzLearningModulesService.getById(id);
        if (inzLearningModules == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzLearningModules);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzLearningModules
     */
    @RequiresPermissions("inz_learning_modules:inz_learning_module:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzLearningModules inzLearningModules) {
        return super.exportXls(request, inzLearningModules, InzLearningModules.class, "继续深造-模块");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("inz_learning_modules:inz_learning_module:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzLearningModules.class);
    }

}
