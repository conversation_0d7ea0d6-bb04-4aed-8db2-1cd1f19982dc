package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.system.entity.UploadTask;

import java.util.Date;
import java.util.List;

/**
 * 分片上传任务Mapper
 * <AUTHOR>
 * @date 2025-08-04
 */
@Mapper
public interface UploadTaskMapper extends BaseMapper<UploadTask> {

    /**
     * 根据文件哈希查找已存在的任务
     * @param fileHash 文件哈希值
     * @param status 任务状态
     * @return 任务列表
     */
    @Select("SELECT * FROM upload_task WHERE file_hash = #{fileHash} AND status = #{status} ORDER BY created_time DESC LIMIT 1")
    UploadTask findByFileHashAndStatus(@Param("fileHash") String fileHash, @Param("status") String status);

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @param uploadedChunks 已上传分片数
     * @return 更新行数
     */
    @Update("UPDATE upload_task SET status = #{status}, uploaded_chunks = #{uploadedChunks}, updated_time = NOW() WHERE id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status, @Param("uploadedChunks") Integer uploadedChunks);

    /**
     * 更新任务最终URL
     * @param taskId 任务ID
     * @param finalUrl 最终文件URL
     * @return 更新行数
     */
    @Update("UPDATE upload_task SET final_url = #{finalUrl}, status = 'COMPLETED', updated_time = NOW() WHERE id = #{taskId}")
    int updateTaskFinalUrl(@Param("taskId") String taskId, @Param("finalUrl") String finalUrl);

    /**
     * 查找过期的任务
     * @param expiredTime 过期时间
     * @return 过期任务列表
     */
    @Select("SELECT * FROM upload_task WHERE expired_time < #{expiredTime} AND status IN ('FAILED', 'CANCELLED')")
    List<UploadTask> findExpiredTasks(@Param("expiredTime") Date expiredTime);

    /**
     * 查找用户的上传任务
     * @param createdBy 创建人
     * @param status 状态(可选)
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("<script>" +
            "SELECT * FROM upload_task WHERE created_by = #{createdBy} " +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "ORDER BY created_time DESC " +
            "<if test='limit != null and limit > 0'>" +
            "LIMIT #{limit}" +
            "</if>" +
            "</script>")
    List<UploadTask> findUserTasks(@Param("createdBy") String createdBy, @Param("status") String status, @Param("limit") Integer limit);

    /**
     * 统计用户上传任务数量
     * @param createdBy 创建人
     * @param status 状态(可选)
     * @return 任务数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM upload_task WHERE created_by = #{createdBy} " +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "</script>")
    int countUserTasks(@Param("createdBy") String createdBy, @Param("status") String status);

    /**
     * 删除过期任务
     * @param expiredTime 过期时间
     * @return 删除行数
     */
    @Update("DELETE FROM upload_task WHERE expired_time < #{expiredTime} AND status IN ('FAILED', 'CANCELLED')")
    int deleteExpiredTasks(@Param("expiredTime") Date expiredTime);

    /**
     * 批量更新任务状态为失败
     * @param taskIds 任务ID列表
     * @param errorMessage 错误信息
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE upload_task SET status = 'FAILED', updated_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='taskIds' item='taskId' open='(' separator=',' close=')'>" +
            "#{taskId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateTasksToFailed(@Param("taskIds") List<String> taskIds);
}
