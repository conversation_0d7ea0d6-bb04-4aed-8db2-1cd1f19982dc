package org.jeecg.modules.book_words.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.book_words.entity.InzBookWords;
import org.jeecg.modules.book_words.mapper.InzBookWordsMapper;
import org.jeecg.modules.book_words.service.IInzBookWordsService;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.mapper.InzWordBooksMapper;
import org.jeecg.modules.inz_words_articles.entity.InzWordsArticles;
import org.jeecg.modules.inz_words_articles.service.IInzWordsArticlesService;
import org.jeecg.modules.words.entity.InzWords;
import org.jeecg.modules.words.service.IInzWordsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 词书单词
 * @Author: jeecg-boot
 * @Date:   2023-05-21
 * @Version: V1.0
 */
@Service
public class InzBookWordsServiceImpl extends ServiceImpl<InzBookWordsMapper, InzBookWords> implements IInzBookWordsService {

    private static final Logger log = LoggerFactory.getLogger(InzBookWordsServiceImpl.class);
    
    @Autowired
    private IInzWordsArticlesService inzWordArticleService;
    
    @Autowired
    @Lazy
    private IInzWordsService inzWordsService;

    @Override
    public void deleteByWordId(String id) {
        LambdaQueryWrapper<InzBookWords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzBookWords::getWordId, id);
        this.remove(queryWrapper);
    }
    
    @Override
    public List<InzBookWords> getWordsByChapter(String bookId, String chapterId) {
        if (bookId == null || chapterId == null) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<InzBookWords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzBookWords::getBookId, bookId);
        queryWrapper.eq(InzBookWords::getChapterId, chapterId);
        
        return this.list(queryWrapper);
    }
    
    @Override
    public void generateArticlesForChapter(String bookId, String chapterId,String fileName) {
        try {
            log.info("========== 开始为章节生成短文 ==========");
            log.info("参数: bookId={}, chapterId={},fileName={}", bookId, chapterId,fileName);
            
            // 获取章节中所有单词
            List<InzBookWords> chapterWords = getWordsByChapter(bookId, chapterId);
            if (CollectionUtils.isEmpty(chapterWords)) {
                log.warn("章节中没有单词，无法生成短文: bookId={}, chapterId={},fileName={}", bookId, chapterId,fileName);
                return;
            }
            log.info("章节中单词数量: {}", chapterWords.size());
            
            // 获取已有短文中的单词ID
            List<String> existingWordIdsInArticles = inzWordArticleService.getWordIdsInChapterArticles(bookId, chapterId);
            log.info("章节中已有短文的单词组数量: {}", existingWordIdsInArticles.size());
            
            // 解析已有短文中的单词ID
            List<String> existingWordIds = new ArrayList<>();
            for (String wordIdsStr : existingWordIdsInArticles) {
                if (StringUtils.isNotBlank(wordIdsStr)) {
                    Collections.addAll(existingWordIds, wordIdsStr.split(","));
                }
            }
            log.info("章节中已有短文的单词ID数量: {}", existingWordIds.size());
            if (!existingWordIds.isEmpty()) {
                log.debug("已有短文的单词ID: {}", existingWordIds);
            }
            
            // 获取单词的实际文本
            List<String> wordIds = chapterWords.stream()
                .map(InzBookWords::getWordId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
                
            log.info("需要查询的单词ID数量: {}", wordIds.size());
            log.debug("需要查询的单词ID列表: {}", wordIds);
            
            List<InzWords> inzWordsList = inzWordsService.listByIds(wordIds);
            log.info("从数据库查询到的单词数量: {}", inzWordsList.size());
            
            // 创建单词ID到单词文本的映射
            Map<String, String> wordIdToText = new HashMap<>();
            List<String> missingWordIds = new ArrayList<>();
            List<String> emptyWordTexts = new ArrayList<>();
            
            for (String wordId : wordIds) {
                boolean found = false;
                for (InzWords word : inzWordsList) {
                    if (wordId.equals(word.getId())) {
                        found = true;
                        if (StringUtils.isNotBlank(word.getWord())) {
                            wordIdToText.put(wordId, word.getWord());
                        } else {
                            emptyWordTexts.add(wordId);
                        }
                        break;
                    }
                }
                if (!found) {
                    missingWordIds.add(wordId);
                }
            }
            
            if (!missingWordIds.isEmpty()) {
                log.warn("以下单词ID在InzWords表中未找到: {}", missingWordIds);
            }
            
            if (!emptyWordTexts.isEmpty()) {
                log.warn("以下单词ID对应的word字段为空: {}", emptyWordTexts);
            }
            
            log.info("有效的单词映射数量: {}", wordIdToText.size());
            
            // 按每组10个单词分组
            int groupSize = 10;
            int totalGroups = (chapterWords.size() + groupSize - 1) / groupSize; // 向上取整
            log.info("单词将被分为 {} 组进行短文生成", totalGroups);
            
            int requiredArticlesPerGroup = 1; // 每组单词需要的短文数量
            int totalArticlesGenerated = 0;
            
            // 获取每个组已有的短文数量
            Map<Integer, Integer> groupArticleCounts = getExistingArticleCountsByGroup(bookId, chapterId);
            log.info("各组已有短文数量: {}", groupArticleCounts);
            
            for (int i = 0; i < totalGroups; i++) {
                int startIdx = i * groupSize;
                int endIdx = Math.min(startIdx + groupSize, chapterWords.size());
                List<InzBookWords> groupWords = chapterWords.subList(startIdx, endIdx);
                
                log.info("========== 处理第 {} 组单词 ==========", i+1);
                log.info("该组单词数量: {}", groupWords.size());
                
                // 收集组内单词ID和单词文本
                List<String> groupWordIds = new ArrayList<>();
                List<String> groupWordTexts = new ArrayList<>();
                Map<String, String> groupWordMap = new LinkedHashMap<>(); // 保持顺序的映射
                
                for (InzBookWords word : groupWords) {
                    // 只有当单词文本不为空时才添加
                    String wordText = wordIdToText.get(word.getWordId());
                    if (StringUtils.isNotBlank(wordText)) {
                        groupWordIds.add(word.getWordId());
                        groupWordTexts.add(wordText);
                        groupWordMap.put(word.getWordId(), wordText);
                    } else {
                        log.warn("跳过单词ID: {}, 原因: 未找到单词文本或单词为空", word.getWordId());
                    }
                }
                
                log.info("该组有效单词数量: {}", groupWordTexts.size());
                log.info("该组单词详情: {}", groupWordMap);
                
                if (!CollectionUtils.isEmpty(groupWordTexts)) {
                    // 计算需要生成的短文数量
                    int groupIndex = i + 1;
                    int existingArticles = groupArticleCounts.getOrDefault(groupIndex, 0);
                    int articlesToGenerate = requiredArticlesPerGroup - existingArticles;
                    
                    if (articlesToGenerate <= 0) {
                        log.info("第 {} 组单词已有 {} 篇短文，达到或超过要求的 {} 篇，无需生成", 
                                 groupIndex, existingArticles, requiredArticlesPerGroup);
                        continue;
                    }
                    
                    log.info("第 {} 组单词已有 {} 篇短文，需要再生成 {} 篇", 
                             groupIndex, existingArticles, articlesToGenerate);
                    
                    log.info("开始为第 {} 组单词生成短文, 单词: {}", groupIndex, String.join(", ", groupWordTexts));
                    int generated = inzWordArticleService.generateAndSaveArticles(
                        groupWordIds, groupWordTexts, bookId, chapterId, groupIndex, articlesToGenerate,fileName
                    );
                    totalArticlesGenerated += generated;
                    log.info("第 {} 组单词成功生成 {} 篇短文", groupIndex, generated);
                } else {
                    log.warn("第 {} 组单词没有有效的单词文本，跳过短文生成", i+1);
                }
            }
            
            log.info("========== 章节短文生成完成 ==========");
            log.info("章节: bookId={}, chapterId={}", bookId, chapterId);
            log.info("总共生成 {} 篇短文", totalArticlesGenerated);
            
        } catch (Exception e) {
            log.error("为章节生成短文时出错: bookId={}, chapterId={}", bookId, chapterId, e);
        }
    }
    
    /**
     * 获取每个组已有的短文数量
     * 
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @return 组索引到短文数量的映射
     */
    private Map<Integer, Integer> getExistingArticleCountsByGroup(String bookId, String chapterId) {
        Map<Integer, Integer> result = new HashMap<>();
        try {
            // 构建查询条件
            LambdaQueryWrapper<InzWordsArticles> query = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(bookId)) {
                query.eq(InzWordsArticles::getBookId, bookId);
            }
            query.eq(InzWordsArticles::getChapterId, chapterId);
            query.select(InzWordsArticles::getGroupIndex);
            
            // 查询所有符合条件的记录
            List<InzWordsArticles> articles = inzWordArticleService.list(query);
            
            // 按组索引统计数量
            for (InzWordsArticles article : articles) {
                Integer groupIndex = article.getGroupIndex();
                if (groupIndex != null) {
                    result.put(groupIndex, result.getOrDefault(groupIndex, 0) + 1);
                }
            }
        } catch (Exception e) {
            log.error("获取组短文数量时出错", e);
        }
        return result;
    }
}
