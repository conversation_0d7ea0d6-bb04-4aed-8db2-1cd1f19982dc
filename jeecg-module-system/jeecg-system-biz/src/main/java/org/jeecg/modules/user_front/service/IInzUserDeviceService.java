package org.jeecg.modules.user_front.service;

import org.jeecg.modules.user_front.entity.InzUserDevice;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 用户登录设备
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface IInzUserDeviceService extends IService<InzUserDevice> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<InzUserDevice>
	 */
	public List<InzUserDevice> selectByMainId(String mainId);
}
