package org.jeecg.modules.user_front.entity;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AdjustBeansDTO {
    @NotNull(message = "操作类型不能为空")
    private Boolean isBatch;

    @NotEmpty(message = "用户ID不能为空")
    private List<String> userIds;

    @NotNull(message = "调整金额不能为空")
    @Min(value = -999999, message = "调整金额最小为-999999")
    @Max(value = 999999, message = "调整金额最大为999999")
    private Integer amount;
}