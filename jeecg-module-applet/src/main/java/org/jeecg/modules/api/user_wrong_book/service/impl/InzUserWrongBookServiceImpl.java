package org.jeecg.modules.api.user_wrong_book.service.impl;

import org.jeecg.modules.api.user_wrong_book.entity.InzUserWrongBook;
import org.jeecg.modules.api.user_wrong_book.mapper.InzUserWrongBookMapper;
import org.jeecg.modules.api.user_wrong_book.service.IInzUserWrongBookService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 错题本
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class InzUserWrongBookServiceImpl extends ServiceImpl<InzUserWrongBookMapper, InzUserWrongBook> implements IInzUserWrongBookService {

}
