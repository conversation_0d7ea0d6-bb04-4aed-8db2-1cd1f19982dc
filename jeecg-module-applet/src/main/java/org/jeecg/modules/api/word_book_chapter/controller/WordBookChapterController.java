package org.jeecg.modules.api.word_book_chapter.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.word_book_chapter.entity.WordBookChapter;
import org.jeecg.modules.api.word_book_chapter.entity.WordBookChapterDto;
import org.jeecg.modules.api.word_book_chapter.service.WordBookChapterService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 词书课节管理
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="H5 - 词书课节管理")
@RestController
@RequestMapping("/word_book_chapter")
@Slf4j
public class WordBookChapterController extends JeecgController<WordBookChapter, WordBookChapterService> {
	@Autowired
	private WordBookChapterService inzWordBookChapterService;

	@Autowired
	private BookWordsService bookWordsService;
	
	 /**
	  * 分页列表查询
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "词书课节管理-分页列表查询")
	 @ApiOperation(value="词书课节列表", notes="词书课节列表")
	 @GetMapping(value = "/listAll")
	 public Result<List<WordBookChapter>> listAll(WordBookChapterDto wordBookChapterDto,
												  HttpServletRequest req) {
		 WordBookChapter inzWordBookChapter = new WordBookChapter();
		 BeanUtils.copyProperties(wordBookChapterDto,inzWordBookChapter);
		 QueryWrapper<WordBookChapter> queryWrapper = QueryGenerator.initQueryWrapper(inzWordBookChapter, req.getParameterMap());
		 queryWrapper.lambda().eq(WordBookChapter::getBookId,inzWordBookChapter.getBookId())
				 .eq(StringUtils.isNotBlank(wordBookChapterDto.getName()),WordBookChapter::getName,inzWordBookChapter.getName())
				 .orderBy(true,true,WordBookChapter::getSort);
		 List<WordBookChapter> pageList = inzWordBookChapterService.list(queryWrapper);
		 pageList.parallelStream().forEach(item -> {
			 item.setWordCount((int) bookWordsService.count(new QueryWrapper<BookWords>().lambda()
					 .eq(BookWords::getChapterId,item.getId()).eq(BookWords::getBookId,item.getBookId())));
		 });
		 return Result.OK(pageList);
	 }

	@ApiOperation(value="inz_book_words-分页列表查询", notes="inz_book_words-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WordBookChapter>> queryPageList(WordBookChapter inzWordBookChapter,
													 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													 HttpServletRequest req) {
		QueryWrapper<WordBookChapter> queryWrapper = QueryGenerator.initQueryWrapper(inzWordBookChapter, req.getParameterMap());
		Page<WordBookChapter> page = new Page<WordBookChapter>(pageNo, pageSize);
		IPage<WordBookChapter> pageList = inzWordBookChapterService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
}
