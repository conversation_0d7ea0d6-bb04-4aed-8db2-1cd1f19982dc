package org.jeecg.modules.api.learning_category.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;

/**
 * @Description: 课程分类表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="inz_learning_category对象", description="课程分类表")
@TableName("inz_learning_category")
public class InzLearningCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**所属模块ID*/
    @Excel(name = "所属模块ID", width = 15)
    @ApiModelProperty(value = "所属模块ID")
    private String moduleId;

    /**分类名称*/
    @Excel(name = "分类名称", width = 15)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**分类编码*/
    @Excel(name = "分类编码", width = 15)
    @ApiModelProperty(value = "分类编码")
    private String categoryCode;

    /**分类描述*/
    @Excel(name = "分类描述", width = 30)
    @ApiModelProperty(value = "分类描述")
    private String description;

    /**封面图片URL*/
    @Excel(name = "封面图片URL", width = 30)
    @ApiModelProperty(value = "封面图片URL")
    private String coverImage;

    /**父分类ID*/
    @Excel(name = "父分类ID", width = 15)
    @ApiModelProperty(value = "父分类ID")
    private String parentId;

    /**层级 1-一级分类 2-二级分类*/
    @Excel(name = "层级", width = 15)
    @ApiModelProperty(value = "层级 1-一级分类 2-二级分类")
    private Integer level;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**状态 0-禁用 1-启用*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private Integer status;

    /**总视频数量*/
    @Excel(name = "总视频数量", width = 15)
    @ApiModelProperty(value = "总视频数量")
    private Integer totalVideos;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**分类下的视频列表（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "分类下的视频列表")
    private List<InzLearningVideo> videos;

    /**模块名称（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /**子分类列表（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "子分类列表")
    private List<InzLearningCategory> children;

    /**父分类名称（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "父分类名称")
    private String parentName;
}
