package org.jeecg.modules.api.user_collection.service.impl;

import org.jeecg.modules.api.user_collection.entity.InzUserCollection;
import org.jeecg.modules.api.user_collection.mapper.InzUserCollectionMapper;
import org.jeecg.modules.api.user_collection.service.IInzUserCollectionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 用户收藏本表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class InzUserCollectionServiceImpl extends ServiceImpl<InzUserCollectionMapper, InzUserCollection> implements IInzUserCollectionService {

    @Override
    public List<InzUserCollection> listAllWithUnits(String id) {
        return baseMapper.listWithUnits(id);
    }
}
