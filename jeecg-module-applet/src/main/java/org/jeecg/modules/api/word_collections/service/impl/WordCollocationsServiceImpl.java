package org.jeecg.modules.api.word_collections.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.mapper.WordCollocationsMapper;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.springframework.stereotype.Service;

/**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-01-16
 * @Version: V1.0
 */
@Service
public class WordCollocationsServiceImpl extends ServiceImpl<WordCollocationsMapper, WordCollocations> implements WordCollocationsService {

}
