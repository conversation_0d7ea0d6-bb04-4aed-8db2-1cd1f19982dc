package org.jeecg.modules.api.user_collection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.user_collection.entity.InzUserCollection;

import java.util.List;

/**
 * @Description: 用户收藏本表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
public interface InzUserCollectionMapper extends BaseMapper<InzUserCollection> {

    List<InzUserCollection> listWithUnits(@Param("id") String id);
}
