package org.jeecg.modules.api.word_collections.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

 /**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-01-16
 * @Version: V1.0
 */
@Api(tags="存储固定搭配")
@RestController
@RequestMapping("/word_collections")
@Slf4j
public class WordCollocationsController extends JeecgController<WordCollocations, WordCollocationsService> {
	@Autowired
	private WordCollocationsService wordCollocationsService;

	/**
	 * 分页列表查询
	 *
	 * @param wordCollocations
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "存储固定搭配-分页列表查询")
	@ApiOperation(value="存储固定搭配-分页列表查询", notes="存储固定搭配-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WordCollocations>> queryPageList(WordCollocations wordCollocations,
														 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														 HttpServletRequest req) {
        QueryWrapper<WordCollocations> queryWrapper = QueryGenerator.initQueryWrapper(wordCollocations, req.getParameterMap());
		Page<WordCollocations> page = new Page<WordCollocations>(pageNo, pageSize);
		IPage<WordCollocations> pageList = wordCollocationsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
}
