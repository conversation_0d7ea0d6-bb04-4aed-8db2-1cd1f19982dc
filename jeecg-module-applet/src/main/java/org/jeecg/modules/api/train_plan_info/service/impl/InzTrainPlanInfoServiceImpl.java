package org.jeecg.modules.api.train_plan_info.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.books.service.WordBooksService;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;
import org.jeecg.modules.api.train_plan.service.IInzTrainPlanService;
import org.jeecg.modules.api.train_plan_info.entity.InzTrainPlanInfo;
import org.jeecg.modules.api.train_plan_info.mapper.InzTrainPlanInfoMapper;
import org.jeecg.modules.api.train_plan_info.service.IInzTrainPlanInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Service
public class InzTrainPlanInfoServiceImpl extends ServiceImpl<InzTrainPlanInfoMapper, InzTrainPlanInfo> implements IInzTrainPlanInfoService {

    @Autowired
    private IInzTrainPlanService iInzTrainPlanService;

    @Autowired
    private BookWordsService bookWordsService;

    @Autowired
    private WordBooksService wordBooksService;

    @Override
    @Async
    public void savePlan(InzTrainPlan trainPlan, List<Map<String, Integer>> learningPlan) {
        String userIdByToken = CommonUtils.getUserIdByToken();
        List<InzTrainPlanInfo> inzTrainPlanInfos = new ArrayList<>();
        BookWords bookWords = new BookWords();
        bookWords.setBookId(trainPlan.getBookId());

        List<InzTrainPlanInfo> infos = list(new QueryWrapper<InzTrainPlanInfo>().lambda().eq(InzTrainPlanInfo::getPlanId, trainPlan.getId()).eq(InzTrainPlanInfo::getStatus, 2));

        List<String> collected = infos.stream().map(InzTrainPlanInfo::getWordId)
                .collect(Collectors.toList());
        if(!collected.isEmpty()){
            bookWords.setWordNIds(collected);
        }

        List<BookWords> list = bookWordsService.listWithWordsLimit(bookWords, trainPlan.getDailyWordsCount() - infos.size(), null);

        // 批量查询已经存在的 wordId
        List<String> wordIds = list.stream()
                .map(BookWords::getWordId)
                .collect(Collectors.toList());

        baseMapper.delete(new QueryWrapper<InzTrainPlanInfo>().lambda().eq(InzTrainPlanInfo::getPlanId, trainPlan.getId())
                .eq(InzTrainPlanInfo::getStatus,1));

        List<String> existingWordIds = iInzTrainPlanService.getExistingWordIdsByPlanId(trainPlan.getId(), wordIds);
        // 过滤掉已存在的 wordId，只保存不存在的
        list.forEach(item -> {
            if (!existingWordIds.contains(item.getWordId())) {
                InzTrainPlanInfo inzTrainPlanInfo = new InzTrainPlanInfo();
                inzTrainPlanInfo.setBookId(trainPlan.getBookId());
                inzTrainPlanInfo.setPlanId(trainPlan.getId());
                inzTrainPlanInfo.setWordId(item.getWordId());
                inzTrainPlanInfo.setCreateBy(userIdByToken);
                inzTrainPlanInfo.setDay(trainPlan.getDay());
                inzTrainPlanInfos.add(inzTrainPlanInfo);
            }
        });

        // 批量保存有效的记录
        if (!inzTrainPlanInfos.isEmpty()) {
            saveBatch(inzTrainPlanInfos);
        }
    }

    @Override
    @Async
    public void updatePlan(InzTrainPlan trainPlan) {
        String userIdByToken = CommonUtils.getUserIdByToken();
        List<InzTrainPlanInfo> inzTrainPlanInfos = new ArrayList<>();
        BookWords bookWords = new BookWords();
        bookWords.setBookId(trainPlan.getBookId());

        List<InzTrainPlanInfo> learnInfo = list(new QueryWrapper<InzTrainPlanInfo>().lambda()
                .eq(InzTrainPlanInfo::getPlanId, trainPlan.getId())
                .eq(InzTrainPlanInfo::getStatus, 2)
                .eq(InzTrainPlanInfo::getLearnType,1));
        // 批量查询已经存在的 wordId
        List<String> wordIds = learnInfo.stream()
                .map(InzTrainPlanInfo::getWordId)
                .collect(Collectors.toList());

        //处理新学单词
        bookWords.setWordIds(wordIds);
        List<BookWords> list = bookWordsService.listWithWordsLimitNotIn(bookWords, trainPlan.getDailyWordsCount(), null);
        List<String> existingWordIds = iInzTrainPlanService.getExistingWordIdsByPlanId(trainPlan.getId(), wordIds);
        // 过滤掉已存在的 wordId，只保存不存在的
        list.forEach(item -> {
            if (!existingWordIds.contains(item.getWordId())) {
                InzTrainPlanInfo inzTrainPlanInfo = new InzTrainPlanInfo();
                inzTrainPlanInfo.setBookId(trainPlan.getBookId());
                inzTrainPlanInfo.setPlanId(trainPlan.getId());
                inzTrainPlanInfo.setWordId(item.getWordId());
                inzTrainPlanInfo.setCreateBy(userIdByToken);
                inzTrainPlanInfo.setDay(trainPlan.getDay());
                inzTrainPlanInfo.setIsLock(0);
                inzTrainPlanInfos.add(inzTrainPlanInfo);
            }
        });

        //处理复习单词
        Integer day = trainPlan.getDay();
        List<String> reviewWordsForDay = getReviewWordsForDay(trainPlan, day);
        reviewWordsForDay.forEach(item -> {
            InzTrainPlanInfo inzTrainPlanInfo = new InzTrainPlanInfo();
            inzTrainPlanInfo.setBookId(trainPlan.getBookId());
            inzTrainPlanInfo.setPlanId(trainPlan.getId());
            inzTrainPlanInfo.setWordId(item);
            inzTrainPlanInfo.setLearnType(2);
            inzTrainPlanInfo.setCreateBy(userIdByToken);
            inzTrainPlanInfo.setDay(trainPlan.getDay());
            inzTrainPlanInfo.setIsLock(0);
            inzTrainPlanInfos.add(inzTrainPlanInfo);
        });

        // 批量保存有效的记录
        if (!inzTrainPlanInfos.isEmpty()) {
            saveBatch(inzTrainPlanInfos);
        }
    }

    // 根据天数计算复习的单词数量
    private List<String> getReviewWordsForDay(InzTrainPlan trainPlan, int day) {
        List<String> reviewWordIds = new ArrayList<>();
        // 复习的天数为前1天、前2天、前4天、前7天、前14天
        int[] reviewDays = {1, 2, 4, 7, 14};
        for (int reviewDay : reviewDays) {
            if (day - reviewDay > 0) {
                List<InzTrainPlanInfo> list = list(new QueryWrapper<InzTrainPlanInfo>().lambda()
                        .eq(InzTrainPlanInfo::getPlanId, trainPlan.getId())
                        .eq(InzTrainPlanInfo::getDay, day - reviewDay)
                        .eq(InzTrainPlanInfo::getLearnType, 1));
                reviewWordIds.addAll(list.stream()
                        .map(InzTrainPlanInfo::getWordId)
                        .collect(Collectors.toList()));
            }
        }
        return reviewWordIds;
    }

    @Override
    public List<BookWords> listWithWords(InzTrainPlanInfo inzTrainPlanInfo) {
        try {
            InzTrainPlan plan = iInzTrainPlanService.getById(inzTrainPlanInfo.getPlanId());
            inzTrainPlanInfo.setDay(plan.getDay());
            if(inzTrainPlanInfo.getLearnType() == null){
                inzTrainPlanInfo.setLearnType(1);
            }

            if(StringUtils.isBlank(inzTrainPlanInfo.getSortBy())){
                inzTrainPlanInfo.setSortBy("asc");
            }

            List<BookWords> list = baseMapper.listWithWords(inzTrainPlanInfo, plan.getDailyWordsCount());
            if(list != null && !list.isEmpty()){
                return list;
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public HashMap<Integer, HashMap<String, Long>> getDayLearnCount(InzTrainPlan inzTrainPlan) {
        HashMap<Integer, HashMap<String, Long>> stringHashMapHashMap = new HashMap<>();
        List<InzTrainPlanInfo> list = list(new QueryWrapper<InzTrainPlanInfo>().lambda()
                .eq(InzTrainPlanInfo::getPlanId, inzTrainPlan.getId()).isNotNull(InzTrainPlanInfo::getFinishDay).eq(InzTrainPlanInfo::getIsFinish,1));
        List<Integer> collect = list.stream().map(InzTrainPlanInfo::getFinishDay).distinct().collect(Collectors.toList());
        collect.forEach(item->{
            HashMap<String, Long> baseMapperDayLearnCount =  baseMapper.getDayLearnCount(item,inzTrainPlan.getId());
            stringHashMapHashMap.put(item,baseMapperDayLearnCount);
        });


        return stringHashMapHashMap;
    }

    @Override
    public int deleteByTrainPlanId(String id) {
        return baseMapper.delete(new QueryWrapper<InzTrainPlanInfo>().lambda().eq(InzTrainPlanInfo::getPlanId,id));
    }
}
