package org.jeecg.modules.api.user_collection_words.service.impl;

import org.jeecg.modules.api.user_collection_words.entity.InzUserCollectionWords;
import org.jeecg.modules.api.user_collection_words.mapper.InzUserCollectionWordsMapper;
import org.jeecg.modules.api.user_collection_words.service.IInzUserCollectionWordsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户收藏单词表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class InzUserCollectionWordsServiceImpl extends ServiceImpl<InzUserCollectionWordsMapper, InzUserCollectionWords> implements IInzUserCollectionWordsService {

}
