import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '收藏本id',
    align:"center",
    dataIndex: 'collectionId'
   },
   {
    title: '收藏本单元id',
    align:"center",
    dataIndex: 'unitId'
   },
   {
    title: '单词id',
    align:"center",
    dataIndex: 'wordId'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '收藏本id',
    field: 'collectionId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入收藏本id!'},
          ];
     },
  },
  {
    label: '收藏本单元id',
    field: 'unitId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入收藏本单元id!'},
          ];
     },
  },
  {
    label: '单词id',
    field: 'wordId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入单词id!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  collectionId: {title: '收藏本id',order: 0,view: 'text', type: 'string',},
  unitId: {title: '收藏本单元id',order: 1,view: 'text', type: 'string',},
  wordId: {title: '单词id',order: 2,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}