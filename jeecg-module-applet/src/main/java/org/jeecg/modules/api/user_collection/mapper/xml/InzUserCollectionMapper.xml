<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.user_collection.mapper.InzUserCollectionMapper">
    <!-- 定义一个 ResultMap，映射 InzUserCollection 和它的关联单元 -->
    <resultMap id="InzUserCollectionResultMap" type="org.jeecg.modules.api.user_collection.entity.InzUserCollection">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sysOrgCode" column="sys_org_code"/>
        <result property="name" column="name"/>
        <result property="isDefault" column="is_default"/>

        <!-- 配置 List 映射 -->
        <collection property="unitList" ofType="org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit">
            <result property="id" column="unit_id"/>
            <result property="collectionId" column="collection_id"/>
            <result property="unitName" column="unit_name"/>
        </collection>
    </resultMap>
    <select id="listWithUnits" resultMap="InzUserCollectionResultMap">
        SELECT
            uc.id, uc.create_by, uc.create_time, uc.update_by, uc.update_time, uc.sys_org_code, uc.name,uc.is_default,
            ucu.id AS unit_id, ucu.collection_id, ucu.unit_name
        FROM inz_user_collection uc
                 LEFT JOIN inz_user_collection_unit ucu ON uc.id = ucu.collection_id
        WHERE uc.create_by = #{id}
    </select>
</mapper>