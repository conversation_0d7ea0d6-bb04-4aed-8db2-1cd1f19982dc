package org.jeecg.modules.api.user_wrong_book.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 错题本
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_wrong_book对象", description="错题本")
public class SaveUserWrongBookRequest implements Serializable {
    private static final long serialVersionUID = 1L;

	/**单词id*/
    @NotEmpty(message = "单词id不可为空")
    @ApiModelProperty(value = "单词id")
    private String wordsId;

}
