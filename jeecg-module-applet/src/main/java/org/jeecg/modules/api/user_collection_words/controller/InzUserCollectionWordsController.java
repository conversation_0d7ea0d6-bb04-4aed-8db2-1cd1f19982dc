package org.jeecg.modules.api.user_collection_words.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import dm.jdbc.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.user_collection_words.entity.BatchAddRequest;
import org.jeecg.modules.api.user_collection_words.entity.InzUserCollectionWords;
import org.jeecg.modules.api.user_collection_words.service.IInzUserCollectionWordsService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 用户收藏单词表
 * @Author: jeecg-boot
 * @Date: 2025-01-14
 * @Version: V1.0
 */
@Api(tags = "H5 - 用户收藏单词表")
@RestController
@RequestMapping("/user_collection_words")
@Slf4j
public class InzUserCollectionWordsController extends JeecgController<InzUserCollectionWords, IInzUserCollectionWordsService> {
    @Autowired
    private IInzUserCollectionWordsService inzUserCollectionWordsService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private BookWordsService bookWordsService;

    /**
     * 分页列表查询
     *
     * @param inzUserCollectionWords
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "用户收藏单词表-分页列表查询")
    @ApiOperation(value = "用户收藏单词", notes = "用户收藏单词")
    @GetMapping(value = "/list")
    public Result<List<Words>> queryPageList(InzUserCollectionWords inzUserCollectionWords,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam(name = "type", defaultValue = "") String type,
                                             HttpServletRequest req) {
        QueryWrapper<InzUserCollectionWords> queryWrapper = QueryGenerator.initQueryWrapper(inzUserCollectionWords, req.getParameterMap());
        queryWrapper.lambda().eq(StringUtil.isNotEmpty(inzUserCollectionWords.getCollectionId()), InzUserCollectionWords::getCollectionId, inzUserCollectionWords.getCollectionId());
        queryWrapper.lambda().eq(StringUtil.isNotEmpty(inzUserCollectionWords.getUnitId()), InzUserCollectionWords::getUnitId, inzUserCollectionWords.getUnitId());
        queryWrapper.lambda().eq(InzUserCollectionWords::getCreateBy, CommonUtils.getUserIdByToken());
        List<InzUserCollectionWords> listed = inzUserCollectionWordsService.list(queryWrapper);
        List<String> wordId = listed.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
        if(wordId.isEmpty()){
            return Result.OK(new ArrayList<>());
        }
        List<Words> wordsList = wordsFrontService.getAllWithCollection(wordId, CommonUtils.getUserIdByToken(),type);
        return Result.OK(wordsList);
    }

    /**
     * 添加
     *
     * @param inzUserCollectionWords
     * @return
     */
    @AutoLog(value = "收藏单词")
    @ApiOperation(value = "收藏单词", notes = "收藏单词")
    @PostMapping(value = "/collection")
    public Result<String> add(@RequestBody InzUserCollectionWords inzUserCollectionWords) {
        String userId = CommonUtils.getUserIdByToken();
        boolean exists = inzUserCollectionWordsService.exists(new QueryWrapper<>(inzUserCollectionWords).lambda()
                .eq(InzUserCollectionWords::getCollectionId, inzUserCollectionWords.getCollectionId())
                .eq(InzUserCollectionWords::getUnitId, inzUserCollectionWords.getUnitId())
                .eq(InzUserCollectionWords::getCreateBy, userId)
                .eq(InzUserCollectionWords::getWordId, inzUserCollectionWords.getWordId()));
        if (exists) {
            return Result.error("您已经收藏过这个单词了");
        }
        inzUserCollectionWords.setCreateBy(CommonUtils.getUserIdByToken());
        inzUserCollectionWordsService.save(inzUserCollectionWords);
        return Result.OK("添加成功！");
    }


    @AutoLog(value = "用户收藏单词表-批量添加收藏单词")
    @ApiOperation(value = "用户收藏单词表-批量添加", notes = "用户收藏单词表-批量添加")
    @PostMapping(value = "/collection/batch")
    public Result<String> addBatch(@RequestBody BatchAddRequest batchAddRequest) {
        String userId = CommonUtils.getUserIdByToken();

        String collectionId = batchAddRequest.getCollectionId();
        String unitId = batchAddRequest.getUnitId();

        Set<String> existingWordIds = new HashSet<>(inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda()
                        .eq(InzUserCollectionWords::getCreateBy, userId)
                        .eq(InzUserCollectionWords::getCollectionId, collectionId)
                        .eq(InzUserCollectionWords::getUnitId, unitId))
                .stream()
                .map(InzUserCollectionWords::getWordId)  // 获取所有已收藏单词的 ID
                .collect(Collectors.toSet()));

        String wordsString = batchAddRequest.getWords();
        List<String> wordList = Arrays.asList(wordsString.split("\\n"));

        List<Words> wordsInfoList = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getWord, wordList));

        Map<String, Words> wordMap = wordsInfoList.stream()
                .collect(Collectors.toMap(Words::getWord, word -> word));

        List<InzUserCollectionWords> inzUserCollectionWordsToSave = new ArrayList<>();

        for (String word : wordList) {
            if (wordMap.containsKey(word) && !existingWordIds.contains(wordMap.get(word).getId())) {
                // 如果单词存在且没有被收藏过，准备添加到收藏表
                Words wordsInfo = wordMap.get(word);
                InzUserCollectionWords inzUserCollectionWord = new InzUserCollectionWords();
                inzUserCollectionWord.setWordId(wordsInfo.getId());
                inzUserCollectionWord.setCreateBy(userId);
                inzUserCollectionWord.setCollectionId(collectionId);
                inzUserCollectionWord.setUnitId(unitId);
                inzUserCollectionWordsToSave.add(inzUserCollectionWord);
            }
        }

        if (!inzUserCollectionWordsToSave.isEmpty()) {
            inzUserCollectionWordsService.saveBatch(inzUserCollectionWordsToSave);
        }

        return Result.OK("批量添加成功！");

    }

    /**
     * 通过id删除
     *
     * @param wordId
     * @return
     */
    @AutoLog(value = "用户取消收藏单词")
    @ApiOperation(value = "用户取消收藏单词", notes = "用户取消收藏单词")
    @DeleteMapping(value = "/cancel")
    public Result<String> delete(@RequestParam(name = "wordId", required = true) String wordId, @RequestParam(name = "collectionId", required = true) String collectionId, @RequestParam(name = "unitId", required = true) String unitId) {
        inzUserCollectionWordsService.remove(new QueryWrapper<InzUserCollectionWords>().lambda()
                .eq(InzUserCollectionWords::getWordId, wordId)
                .eq(InzUserCollectionWords::getCreateBy, CommonUtils.getUserIdByToken())
                .eq(InzUserCollectionWords::getCollectionId, collectionId)
                .eq(InzUserCollectionWords::getUnitId, unitId));
        return Result.OK("删除成功!");
    }

    /**
     * 通过id删除
     *
     * @param wordIds
     * @return
     */
    @AutoLog(value = "用户批量取消收藏单词")
    @ApiOperation(value = "用户批量取消收藏单词", notes = "用户批量取消收藏单词")
    @DeleteMapping(value = "/cancelBatch")
    public Result<String> cancelBatch(@RequestParam(name = "wordIds", required = true) String wordIds, @RequestParam(name = "collectionId", required = true) String collectionId, @RequestParam(name = "unitId", required = true) String unitId) {
        List<String> wordList = Arrays.asList(wordIds.split(","));
        inzUserCollectionWordsService.remove(new QueryWrapper<InzUserCollectionWords>().lambda()
                .in(InzUserCollectionWords::getWordId, wordList)
                .eq(InzUserCollectionWords::getCreateBy, CommonUtils.getUserIdByToken())
                .eq(InzUserCollectionWords::getCollectionId, collectionId)
                .eq(InzUserCollectionWords::getUnitId, unitId));
        return Result.OK("删除成功!");
    }
}
