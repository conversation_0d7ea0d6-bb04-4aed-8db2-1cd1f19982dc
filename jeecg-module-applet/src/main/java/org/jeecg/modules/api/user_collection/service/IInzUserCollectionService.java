package org.jeecg.modules.api.user_collection.service;

import org.jeecg.modules.api.user_collection.entity.InzUserCollection;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 用户收藏本表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
public interface IInzUserCollectionService extends IService<InzUserCollection> {

    List<InzUserCollection> listAllWithUnits(String id);
}
