package org.jeecg.modules.api.learning_category.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.learning_category.entity.InzLearningCategory;
import org.jeecg.modules.api.learning_category.mapper.InzLearningCategoryMapper;
import org.jeecg.modules.api.learning_category.service.IInzLearningCategoryService;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;
import org.jeecg.modules.api.learning_video.service.IInzLearningVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 课程分类表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class InzLearningCategoryServiceImpl extends ServiceImpl<InzLearningCategoryMapper, InzLearningCategory> implements IInzLearningCategoryService {

    @Autowired
    private InzLearningCategoryMapper learningCategoryMapper;

    @Autowired
    private IInzLearningVideoService learningVideoService;

    @Override
    public List<InzLearningCategory> getCategoriesByModuleId(String moduleId) {
        return learningCategoryMapper.getCategoriesByModuleId(moduleId);
    }

    @Override
    public List<InzLearningCategory> getCategoriesWithVideos(String moduleId) {
        // 获取分类列表
        List<InzLearningCategory> categories = learningCategoryMapper.getCategoriesWithModule(moduleId);

        // 为每个分类加载视频列表
        for (InzLearningCategory category : categories) {
            List<InzLearningVideo> videos = learningVideoService.getVideosByCategoryId(category.getId());

            // 处理视频URL数组
            for (InzLearningVideo video : videos) {
                if (video.getVideoUrl() != null && !video.getVideoUrl().isEmpty()) {
                    try {
                        // 尝试解析JSON数组
                        List<String> urlList = JSON.parseArray(video.getVideoUrl(), String.class);
                        video.setVideoUrls(urlList);
                    } catch (Exception e) {
                        // 如果不是JSON格式，则作为单个URL处理
                        List<String> urlList = new ArrayList<>();
                        urlList.add(video.getVideoUrl());
                        video.setVideoUrls(urlList);
                    }
                }
            }

            category.setVideos(videos);
        }

        return categories;
    }

    @Override
    public InzLearningCategory getByCategoryCode(String categoryCode) {
        return learningCategoryMapper.getByCategoryCode(categoryCode);
    }

    @Override
    public List<InzLearningCategory> getCategoryTree(String moduleId) {
        // 获取一级分类
        List<InzLearningCategory> firstLevelCategories = getFirstLevelCategories(moduleId);

        // 为每个一级分类加载子分类
        for (InzLearningCategory category : firstLevelCategories) {
            List<InzLearningCategory> children = getSubCategories(category.getId());
            category.setChildren(children);
        }

        return firstLevelCategories;
    }

    @Override
    public List<InzLearningCategory> getFirstLevelCategories(String moduleId) {
        QueryWrapper<InzLearningCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("module_id", moduleId);
        queryWrapper.eq("level", 1); // 一级分类
        queryWrapper.eq("status", 1); // 启用状态
        queryWrapper.orderByAsc("sort_order");
        return this.list(queryWrapper);
    }

    @Override
    public List<InzLearningCategory> getSubCategories(String parentId) {
        QueryWrapper<InzLearningCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.eq("level", 2); // 二级分类
        queryWrapper.eq("status", 1); // 启用状态
        queryWrapper.orderByAsc("sort_order");
        return this.list(queryWrapper);
    }
}
