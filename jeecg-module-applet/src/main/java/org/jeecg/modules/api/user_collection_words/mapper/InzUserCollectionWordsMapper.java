package org.jeecg.modules.api.user_collection_words.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.user_collection_words.entity.InzUserCollectionWords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户收藏单词表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
public interface InzUserCollectionWordsMapper extends BaseMapper<InzUserCollectionWords> {

}
