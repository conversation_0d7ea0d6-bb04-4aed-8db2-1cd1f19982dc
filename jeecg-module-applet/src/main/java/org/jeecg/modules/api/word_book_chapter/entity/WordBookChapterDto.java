package org.jeecg.modules.api.word_book_chapter.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 词书课节管理
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_word_book_chapter对象", description="词书课节管理")
public class WordBookChapterDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "请选择词书")
    @ApiModelProperty(value = "所属词书",required = true)
    private String bookId;
	/**课节名称*/
    @ApiModelProperty(value = "课节名称")
    private String name;
}
