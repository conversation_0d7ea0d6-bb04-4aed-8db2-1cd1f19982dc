package org.jeecg.modules.api.user_collection_unit.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit;
import org.jeecg.modules.api.user_collection_unit.service.IInzUserCollectionUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 用户收藏单元表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="用户收藏单元表")
@RestController
@RequestMapping("/user_collection_unit")
@Slf4j
public class InzUserCollectionUnitController extends JeecgController<InzUserCollectionUnit, IInzUserCollectionUnitService> {
	@Autowired
	private IInzUserCollectionUnitService inzUserCollectionUnitService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzUserCollectionUnit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户收藏单元表-分页列表查询")
	@ApiOperation(value="用户收藏单元表-分页列表查询", notes="用户收藏单元表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzUserCollectionUnit>> queryPageList(InzUserCollectionUnit inzUserCollectionUnit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String id = loginUser.getId();
        QueryWrapper<InzUserCollectionUnit> queryWrapper = QueryGenerator.initQueryWrapper(inzUserCollectionUnit, req.getParameterMap());
		queryWrapper.lambda().eq(InzUserCollectionUnit::getCollectionId, inzUserCollectionUnit.getCollectionId());
		queryWrapper.lambda().eq(InzUserCollectionUnit::getCreateBy, id);
		Page<InzUserCollectionUnit> page = new Page<InzUserCollectionUnit>(pageNo, pageSize);
		IPage<InzUserCollectionUnit> pageList = inzUserCollectionUnitService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzUserCollectionUnit
	 * @return
	 */
	@AutoLog(value = "用户收藏单元表-添加")
	@ApiOperation(value="用户收藏单元表-添加", notes="用户收藏单元表-添加")
	@RequiresPermissions("user_collection_unit:inz_user_collection_unit:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzUserCollectionUnit inzUserCollectionUnit) {
		boolean exists = inzUserCollectionUnitService.exists(new QueryWrapper<>(inzUserCollectionUnit).lambda()
				.eq(InzUserCollectionUnit::getCollectionId, inzUserCollectionUnit.getCollectionId())
				.eq(InzUserCollectionUnit::getUnitName, inzUserCollectionUnit.getUnitName()));
		if(exists) {
			return Result.error("此收藏本下已有相同名称的单元，请勿重复");
		}
		inzUserCollectionUnitService.save(inzUserCollectionUnit);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzUserCollectionUnit
	 * @return
	 */
	@AutoLog(value = "用户收藏单元表-编辑")
	@ApiOperation(value="用户收藏单元表-编辑", notes="用户收藏单元表-编辑")
	@RequiresPermissions("user_collection_unit:inz_user_collection_unit:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzUserCollectionUnit inzUserCollectionUnit) {
		boolean exists = inzUserCollectionUnitService.exists(new QueryWrapper<>(inzUserCollectionUnit).lambda()
				.eq(InzUserCollectionUnit::getCollectionId, inzUserCollectionUnit.getCollectionId())
				.eq(InzUserCollectionUnit::getUnitName, inzUserCollectionUnit.getUnitName())
				.ne(InzUserCollectionUnit::getId, inzUserCollectionUnit.getId()));
		if(exists) {
			return Result.error("此收藏本下已有相同名称的单元，请勿重复");
		}
		inzUserCollectionUnitService.updateById(inzUserCollectionUnit);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户收藏单元表-通过id删除")
	@ApiOperation(value="用户收藏单元表-通过id删除", notes="用户收藏单元表-通过id删除")
	@RequiresPermissions("user_collection_unit:inz_user_collection_unit:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzUserCollectionUnitService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户收藏单元表-批量删除")
	@ApiOperation(value="用户收藏单元表-批量删除", notes="用户收藏单元表-批量删除")
	@RequiresPermissions("user_collection_unit:inz_user_collection_unit:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzUserCollectionUnitService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户收藏单元表-通过id查询")
	@ApiOperation(value="用户收藏单元表-通过id查询", notes="用户收藏单元表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzUserCollectionUnit> queryById(@RequestParam(name="id",required=true) String id) {
		InzUserCollectionUnit inzUserCollectionUnit = inzUserCollectionUnitService.getById(id);
		if(inzUserCollectionUnit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzUserCollectionUnit);
	}
}
