package org.jeecg.modules.api.train_plan_info.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;
import org.jeecg.modules.api.train_plan_info.entity.InzTrainPlanInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
public interface IInzTrainPlanInfoService extends IService<InzTrainPlanInfo> {

    void savePlan(InzTrainPlan inzTrainPlan, List<Map<String, Integer>> learningPlan);

    void updatePlan(InzTrainPlan inzTrainPlan);

    List<BookWords> listWithWords(InzTrainPlanInfo inzTrainPlanInfo);

    HashMap<Integer, HashMap<String, Long>> getDayLearnCount(InzTrainPlan inzTrainPlan);

    int deleteByTrainPlanId(String id);
}
