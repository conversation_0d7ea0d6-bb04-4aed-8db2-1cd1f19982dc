package org.jeecg.modules.api.user_collection.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_collection.entity.InzUserCollection;
import org.jeecg.modules.api.user_collection.entity.SaveUserCollectionDto;
import org.jeecg.modules.api.user_collection.service.IInzUserCollectionService;
import org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit;
import org.jeecg.modules.api.user_collection_unit.service.IInzUserCollectionUnitService;
import org.jeecg.modules.api.user_collection_words.entity.InzUserCollectionWords;
import org.jeecg.modules.api.user_collection_words.service.IInzUserCollectionWordsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

 /**
 * @Description: 用户收藏本表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="H5 - 用户收藏本表")
@RestController
@RequestMapping("/user_collection")
@Slf4j
public class InzUserCollectionController extends JeecgController<InzUserCollection, IInzUserCollectionService> {
	@Autowired
	private IInzUserCollectionService inzUserCollectionService;

	@Autowired
	private IInzUserCollectionUnitService iInzUserCollectionUnitService;

	@Autowired
	private IInzUserCollectionWordsService iInzUserCollectionWordsService;

	/**
	 * 分页列表查询
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户收藏本表-分页列表查询")
	@ApiOperation(value="用户收藏本表-分页列表查询", notes="用户收藏本表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzUserCollection>> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		InzUserCollection inzUserCollection = new InzUserCollection();
		QueryWrapper<InzUserCollection> queryWrapper = QueryGenerator.initQueryWrapper(inzUserCollection, req.getParameterMap());
		queryWrapper.lambda().eq(InzUserCollection::getCreateBy, CommonUtils.getUserIdByToken());
		Page<InzUserCollection> page = new Page<InzUserCollection>(pageNo, pageSize);
		IPage<InzUserCollection> pageList = inzUserCollectionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 无分页列表
	  * @return
	  */
	 //@AutoLog(value = "用户收藏本表-分页列表查询")
	 @ApiOperation(value="用户收藏本-无分页列表", notes="用户收藏本-无分页列表")
	 @GetMapping(value = "/listAll")
	 public Result<List<InzUserCollection>> listAll() {
		 List<InzUserCollection> list = inzUserCollectionService.listAllWithUnits(CommonUtils.getUserIdByToken());
		 String userIdByToken = CommonUtils.getUserIdByToken();
		 list.forEach(item->{
			 item.setWordCount((int) iInzUserCollectionWordsService.count(new QueryWrapper<InzUserCollectionWords>().lambda()
					 .eq(InzUserCollectionWords::getCreateBy,userIdByToken)
					 .eq(InzUserCollectionWords::getCollectionId,item.getId())));
		 });
		 return Result.OK(list);
	 }

	/**
	 *   添加
	 * @return
	 */
	@Transactional
	@AutoLog(value = "用户收藏本表-添加")
	@ApiOperation(value="用户收藏本表-添加", notes="用户收藏本表-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@Valid @RequestBody SaveUserCollectionDto inzUserCollectionDto) {
		InzUserCollection inzUserCollection = new InzUserCollection();
		BeanUtils.copyProperties(inzUserCollectionDto, inzUserCollection);
		if(inzUserCollectionService.exists(new QueryWrapper<>(inzUserCollection).lambda().eq(InzUserCollection::getCreateBy, CommonUtils.getUserIdByToken()).eq(InzUserCollection::getName, inzUserCollection.getName()))){
			return Result.error("当前收藏本名称已存在","");
		}

		if(inzUserCollection.getIsDefault()  == 1){
			new InzUserCollection().setIsDefault(1);
			inzUserCollectionService.update(new InzUserCollection().setIsDefault(0),new QueryWrapper<InzUserCollection>().lambda().eq(InzUserCollection::getCreateBy, CommonUtils.getUserIdByToken()));
		}
		inzUserCollection.setCreateBy(CommonUtils.getUserIdByToken());

		inzUserCollectionService.save(inzUserCollection);
		inzUserCollection.getUnitList().forEach(obj->{
			obj.setCreateBy(CommonUtils.getUserIdByToken());
			obj.setCollectionId(inzUserCollection.getId());
		});
		iInzUserCollectionUnitService.saveBatch(inzUserCollection.getUnitList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 * @return
	 */
	@Transactional
	@AutoLog(value = "用户收藏本表-编辑")
	@ApiOperation(value="用户收藏本表-编辑", notes="用户收藏本表-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@Valid @RequestBody SaveUserCollectionDto inzUserCollectionDto) {
		InzUserCollection inzUserCollection = new InzUserCollection();
		BeanUtils.copyProperties(inzUserCollectionDto, inzUserCollection);
		if(inzUserCollectionService.exists(new QueryWrapper<>(inzUserCollection).lambda().eq(InzUserCollection::getCreateBy, CommonUtils.getUserIdByToken()).ne(InzUserCollection::getId,inzUserCollection.getId()).eq(InzUserCollection::getName, inzUserCollection.getName()))){
			return Result.error("当前收藏本名称已存在","");
		}

		if(inzUserCollection.getIsDefault() == 1){
			inzUserCollectionService.update(new InzUserCollection().setIsDefault(0),new QueryWrapper<InzUserCollection>().lambda().eq(InzUserCollection::getCreateBy, CommonUtils.getUserIdByToken()));
		}
		ArrayList<String> strings = new ArrayList<>();
		inzUserCollectionService.updateById(inzUserCollection);
		inzUserCollection.getUnitList().forEach(obj->{
			if(StringUtils.isNotBlank(obj.getId())){
				iInzUserCollectionUnitService.updateById(obj);
				strings.add(obj.getId());
			} else {
				obj.setCreateBy(CommonUtils.getUserIdByToken());
				obj.setCollectionId(inzUserCollection.getId());
				iInzUserCollectionUnitService.save(obj);
			}
		});

		if(strings.isEmpty()){
			iInzUserCollectionUnitService.remove(new QueryWrapper<InzUserCollectionUnit>().lambda()
					.notIn(InzUserCollectionUnit::getId, strings)
					.eq(InzUserCollectionUnit::getCollectionId, inzUserCollection.getId()));
			iInzUserCollectionWordsService.remove(new QueryWrapper<InzUserCollectionWords>().lambda()
					.notIn(InzUserCollectionWords::getUnitId, strings)
					.eq(InzUserCollectionWords::getCollectionId, inzUserCollection.getId()));
		}



        return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户收藏本表-通过id删除")
	@ApiOperation(value="用户收藏本表-通过id删除", notes="用户收藏本表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzUserCollectionService.removeById(id);
		iInzUserCollectionUnitService.remove(new QueryWrapper<InzUserCollectionUnit>().lambda().eq(InzUserCollectionUnit::getCollectionId,id).eq(InzUserCollectionUnit::getCreateBy,CommonUtils.getUserIdByToken()));
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户收藏本表-批量删除")
	@ApiOperation(value="用户收藏本表-批量删除", notes="用户收藏本表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzUserCollectionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户收藏本表-通过id查询")
	@ApiOperation(value="用户收藏本表-通过id查询", notes="用户收藏本表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzUserCollection> queryById(@RequestParam(name="id",required=true) String id) {
		InzUserCollection inzUserCollection = inzUserCollectionService.getById(id);
		if(inzUserCollection==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzUserCollection);
	}
}
