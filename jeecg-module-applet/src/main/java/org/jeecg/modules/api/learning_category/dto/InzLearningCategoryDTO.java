package org.jeecg.modules.api.learning_category.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 学习分类DTO
 * @Author: Alex (工程师)
 * @Date: 2025-08-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "InzLearningCategoryDTO", description = "学习分类数据传输对象")
public class InzLearningCategoryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "模块ID")
    private String moduleId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "分类编码")
    private String categoryCode;

    @ApiModelProperty(value = "分类描述")
    private String description;

    @ApiModelProperty(value = "封面图片URL（完整路径）")
    private String coverImage;

    @ApiModelProperty(value = "父分类ID")
    private String parentId;

    @ApiModelProperty(value = "分类层级")
    private Integer level;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private Integer status;

    @ApiModelProperty(value = "视频总数")
    private Integer totalVideos;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
