package org.jeecg.modules.api.user_collection_unit.service.impl;

import org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit;
import org.jeecg.modules.api.user_collection_unit.mapper.InzUserCollectionUnitMapper;
import org.jeecg.modules.api.user_collection_unit.service.IInzUserCollectionUnitService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户收藏单元表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class InzUserCollectionUnitServiceImpl extends ServiceImpl<InzUserCollectionUnitMapper, InzUserCollectionUnit> implements IInzUserCollectionUnitService {

}
