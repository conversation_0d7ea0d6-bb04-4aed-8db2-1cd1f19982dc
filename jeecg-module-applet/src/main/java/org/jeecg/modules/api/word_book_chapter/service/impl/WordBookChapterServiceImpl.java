package org.jeecg.modules.api.word_book_chapter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.word_book_chapter.entity.WordBookChapter;
import org.jeecg.modules.api.word_book_chapter.mapper.WordBookChapterMapper;
import org.jeecg.modules.api.word_book_chapter.service.WordBookChapterService;
import org.springframework.stereotype.Service;

/**
 * @Description: 词书课节管理
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class WordBookChapterServiceImpl extends ServiceImpl<WordBookChapterMapper, WordBookChapter> implements WordBookChapterService {

}
