package org.jeecg.modules.api.word_collections.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-01-16
 * @Version: V1.0
 */
@Data
@TableName("inz_word_collocations")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_word_collocations对象", description="存储固定搭配")
public class WordCollocations implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**单词id*/
	@Excel(name = "单词id", width = 15)
    @ApiModelProperty(value = "单词id")
    private String wordId;
	/**英文固定搭配*/
	@Excel(name = "英文固定搭配", width = 15)
    @ApiModelProperty(value = "英文固定搭配")
    private String english;
	/**中文含义*/
	@Excel(name = "中文含义", width = 15)
    @ApiModelProperty(value = "中文含义")
    private String chinese;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型   examine例句 collection固定搭配 natural_phonics自然拼读  part_of_speech词性 root_particles 拆分单词  root_breakdown词根拆分 speak_naturl_phonics单词的拼读顺序 transformation 变形")
    private String type;

    @ApiModelProperty(value = "音频文件")
    private String audioUrl;

    @ApiModelProperty(value = "音频文件")
    private Integer sort;

    @TableField(exist = false)
    private String formattedType;

    /**
     * 格式化类型字段
     */
//    public String getFormattedType() {
//        if("part_of_speech".equals(this.type)){
//            if ("Noun".equalsIgnoreCase(this.english)) {
//                return "n";  // 格式化为 "n"
//            } else if ("Verb".equalsIgnoreCase(this.english)) {
//                return "v";  // 格式化为 "v"
//            } else if ("adjective".equalsIgnoreCase(this.english) || "Adjective".equalsIgnoreCase(this.english)) {
//                return "adj";  // 格式化为 "v"
//            } else if ("Adverb".equalsIgnoreCase(this.english)) {
//                return "adv";
//            } else if ("Conjunction".equalsIgnoreCase(this.english)){
//                return "conj";
//            }
//            return this.english;
//        } else {
//            return "";
//        }
//    }

    public String getFormattedType() {
        if ("part_of_speech".equals(this.type)) {
            String lowerEnglish = this.english.toLowerCase(); // 统一转为小写，简化后续判断
            switch (lowerEnglish) {
                case "noun":
                    return "n";
                case "verb":
                    return "v";
                case "adjective":
                    return "adj";
                case "adverb":
                    return "adv";
                case "conjunction":
                    return "conj";
                case "pronoun":
                    return "pron";
                case "preposition":
                    return "prep";
                case "interjection":
                    return "interj";
                case "numeral":
                    return "num";
                case "article":
                    return "art"; // 或 "det"（若归为 Determiner）
                case "determiner":
                    return "det";
                case "participle":
                    return "participle"; // 无通用缩写，可保留原词或自定义
                default:
                    return this.english; // 未知词性返回原值
            }
        } else {
            return "";
        }
    }
}
