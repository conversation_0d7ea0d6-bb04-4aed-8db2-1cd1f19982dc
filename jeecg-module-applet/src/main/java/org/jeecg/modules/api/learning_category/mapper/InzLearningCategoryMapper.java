package org.jeecg.modules.api.learning_category.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.learning_category.entity.InzLearningCategory;

import java.util.List;

/**
 * @Description: 课程分类表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface InzLearningCategoryMapper extends BaseMapper<InzLearningCategory> {

    /**
     * 根据模块ID查询分类列表
     * @param moduleId 模块ID
     * @return 分类列表
     */
    List<InzLearningCategory> getCategoriesByModuleId(@Param("moduleId") String moduleId);

    /**
     * 查询分类详情（包含模块名称）
     * @param moduleId 模块ID
     * @return 分类列表
     */
    List<InzLearningCategory> getCategoriesWithModule(@Param("moduleId") String moduleId);

    /**
     * 更新分类统计信息
     * @param categoryId 分类ID
     * @param totalVideos 总视频数
     */
    void updateCategoryStats(@Param("categoryId") String categoryId,
                           @Param("totalVideos") Integer totalVideos);



    /**
     * 根据分类编码查询分类
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    InzLearningCategory getByCategoryCode(@Param("categoryCode") String categoryCode);


}
