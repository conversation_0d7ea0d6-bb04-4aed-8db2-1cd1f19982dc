package org.jeecg.modules.api.learning_category.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.learning_category.entity.InzLearningCategory;

import java.util.List;

/**
 * @Description: 课程分类表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzLearningCategoryService extends IService<InzLearningCategory> {

    /**
     * 根据模块ID获取分类列表
     * @param moduleId 模块ID
     * @return 分类列表
     */
    List<InzLearningCategory> getCategoriesByModuleId(String moduleId);

    /**
     * 根据模块ID获取分类列表（包含视频信息）
     * @param moduleId 模块ID
     * @return 分类列表（每个分类包含其下的视频列表）
     */
    List<InzLearningCategory> getCategoriesWithVideos(String moduleId);

    /**
     * 根据分类编码获取分类
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    InzLearningCategory getByCategoryCode(String categoryCode);

    /**
     * 获取分类树结构（一级分类及其子分类）
     * @param moduleId 模块ID
     * @return 分类树列表
     */
    List<InzLearningCategory> getCategoryTree(String moduleId);

    /**
     * 获取一级分类列表
     * @param moduleId 模块ID
     * @return 一级分类列表
     */
    List<InzLearningCategory> getFirstLevelCategories(String moduleId);

    /**
     * 根据父分类ID获取子分类列表
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<InzLearningCategory> getSubCategories(String parentId);

}
