package org.jeecg.modules.api.user_collection_words.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="批量导入单词", description="用户收藏单词表")
public class BatchAddRequest {

    @ApiModelProperty(value = "单词")
    private String words;
    @ApiModelProperty(value = "收藏本id")
    private String collectionId;
    @ApiModelProperty(value = "收藏单元")
    private String unitId;
}
