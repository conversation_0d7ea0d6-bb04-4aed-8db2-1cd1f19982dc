package org.jeecg.modules.api.user_collection.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 用户收藏本表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_collection对象", description="用户收藏本表")
public class SaveUserCollectionDto implements Serializable {


    @ApiModelProperty(value = "收藏本id")
    private String id;
	/**收藏本名称*/
	@NotEmpty(message = "请填写收藏本名称")
    @ApiModelProperty(value = "收藏本名称",required = true)
    private String name;

    /**收藏本名称*/
    @NotNull(message = "请选择是否为默认")
    @ApiModelProperty(value = "是否默认 1是  0否",required = true)
    private Integer isDefault;

    @ApiModelProperty(value = "收藏本单元")
    private List<InzUserCollectionUnit> unitList;

}
