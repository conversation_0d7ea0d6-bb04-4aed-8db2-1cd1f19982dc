<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.learning_category.mapper.InzLearningCategoryMapper">

    <!-- 根据模块ID查询分类列表 -->
    <select id="getCategoriesByModuleId" resultType="org.jeecg.modules.api.learning_category.entity.InzLearningCategory">
        SELECT * FROM inz_learning_category
        WHERE module_id = #{moduleId}
        AND status = 1
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 查询分类详情（包含模块名称） -->
    <select id="getCategoriesWithModule" resultType="org.jeecg.modules.api.learning_category.entity.InzLearningCategory">
        SELECT
            c.*,
            m.module_name as moduleName
        FROM inz_learning_category c
        LEFT JOIN inz_learning_module m ON c.module_id = m.id
        WHERE c.module_id = #{moduleId}
        AND c.status = 1
        ORDER BY c.sort_order ASC, c.create_time DESC
    </select>

    <!-- 更新分类统计信息 -->
    <update id="updateCategoryStats">
        UPDATE inz_learning_category
        SET total_videos = #{totalVideos},
            update_time = NOW()
        WHERE id = #{categoryId}
    </update>

    <!-- 根据分类编码查询分类 -->
    <select id="getByCategoryCode" resultType="org.jeecg.modules.api.learning_category.entity.InzLearningCategory">
        SELECT * FROM inz_learning_category
        WHERE category_code = #{categoryCode}
    </select>



</mapper>
