package org.jeecg.modules.api.user_wrong_book.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_wrong_book.entity.InzUserWrongBook;
import org.jeecg.modules.api.user_wrong_book.entity.SaveUserWrongBookRequest;
import org.jeecg.modules.api.user_wrong_book.service.IInzUserWrongBookService;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 * @Description: 错题本
 * @Author: jeecg-boot
 * @Date: 2025-01-14
 * @Version: V1.0
 */
@Api(tags = "H5 - 错题本")
@RestController
@RequestMapping("/user_wrong_book")
@Slf4j
public class InzUserWrongBookController extends JeecgController<InzUserWrongBook, IInzUserWrongBookService> {
    @Autowired
    private IInzUserWrongBookService inzUserWrongBookService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private WordCollocationsService wordCollocationsService;

    /**
     * 分页列表查询
     *
     * @param inzUserWrongBook
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "错题本-分页列表查询")
    @ApiOperation(value = "错题本-分页列表查询", notes = "错题本-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzUserWrongBook>> queryPageList(SaveUserWrongBookRequest saveUserWrongBookRequest,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         @RequestParam(name = "sortBy", defaultValue = "create_time") String sortBy,
                                                         HttpServletRequest req) {
        InzUserWrongBook inzUserWrongBook = new InzUserWrongBook();
        BeanUtils.copyProperties(saveUserWrongBookRequest,inzUserWrongBook);
        QueryWrapper<InzUserWrongBook> queryWrapper = QueryGenerator.initQueryWrapper(inzUserWrongBook, req.getParameterMap());
        queryWrapper.lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken());
        if ("rand".equals(sortBy)) {
            queryWrapper.lambda().orderBy(true, true, InzUserWrongBook::getId);
        }
        Page<InzUserWrongBook> page = new Page<InzUserWrongBook>(pageNo, pageSize);
        IPage<InzUserWrongBook> pageList = inzUserWrongBookService.page(page, queryWrapper);
        pageList.getRecords().forEach(item->{
            item.setWords(wordsFrontService.getById(item.getWordsId()));
            item.setCollocationsList(wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda().eq(WordCollocations::getWordId,item.getWordsId()).orderBy(true, true, WordCollocations::getSort)));
        });
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     * @param req
     * @return
     */
    //@AutoLog(value = "错题本-分页列表查询")
    @ApiOperation(value = "错题本-不带分页", notes = "错题本-不带分页")
    @GetMapping(value = "/listAll")
    public Result<List<InzUserWrongBook>> listAll(@RequestParam(name = "sortBy", defaultValue = "create_time") String sortBy,
                                                  HttpServletRequest req) {
        InzUserWrongBook inzUserWrongBook = new InzUserWrongBook();
        QueryWrapper<InzUserWrongBook> queryWrapper = QueryGenerator.initQueryWrapper(inzUserWrongBook, req.getParameterMap());
        queryWrapper.lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken());
        if ("rand".equals(sortBy)) {
            queryWrapper.last("ORDER BY RAND()");
        }
        List<InzUserWrongBook> pageList = inzUserWrongBookService.list(queryWrapper);
        pageList.forEach(item->{
            item.setWords(wordsFrontService.getById(item.getWordsId()));
            item.setCollocationsList(wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda().eq(WordCollocations::getWordId,item.getWordsId()).orderBy(true, true, WordCollocations::getSort)));
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     * @return
     */
    @AutoLog(value = "错题本-添加")
    @ApiOperation(value = "错题本-添加", notes = "错题本-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@Valid @RequestBody SaveUserWrongBookRequest saveUserWrongBookRequest) {
        InzUserWrongBook inzUserWrongBook = new InzUserWrongBook();
        BeanUtils.copyProperties(saveUserWrongBookRequest,inzUserWrongBook);
        inzUserWrongBook.setCreateBy(CommonUtils.getUserIdByToken());
        if (inzUserWrongBookService.exists(new QueryWrapper<>(inzUserWrongBook).lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()).eq(InzUserWrongBook::getWordsId, saveUserWrongBookRequest.getWordsId()))) {
            return Result.OK("添加成功");
        }
        inzUserWrongBookService.save(inzUserWrongBook);
        return Result.OK("添加成功！");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "错题本-通过id删除")
    @ApiOperation(value = "错题本-通过id删除", notes = "错题本-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzUserWrongBookService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "错题本-批量删除")
    @ApiOperation(value = "错题本-批量删除", notes = "错题本-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzUserWrongBookService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "错题本-通过id查询")
    @ApiOperation(value = "错题本-通过id查询", notes = "错题本-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzUserWrongBook> queryById(@RequestParam(name = "id", required = true) String id) {
        InzUserWrongBook inzUserWrongBook = inzUserWrongBookService.getById(id);
        if (inzUserWrongBook == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzUserWrongBook);
    }
}
