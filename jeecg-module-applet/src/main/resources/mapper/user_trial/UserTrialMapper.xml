<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.user_trial.mapper.UserTrialMapper">

    <!-- 分页查询用户试用历史记录 -->
    <select id="selectTrialHistoryPage" resultType="org.jeecg.modules.api.user_trial.vo.TrialHistoryVO">
        SELECT 
            t.id,
            t.trial_days as trialDays,
            t.start_date as startDate,
            t.end_date as endDate,
            t.status,
            CASE t.status 
                WHEN 1 THEN '有效' 
                ELSE '无效' 
            END as statusText,
            t.create_time as createTime,
            t.remark,
            CASE 
                WHEN t.end_date >= CURDATE() AND t.status = 1 THEN true
                ELSE false 
            END as isCurrent
        FROM inz_user_trial_log t
        WHERE t.user_id = #{userId}
        ORDER BY t.create_time DESC
    </select>

    <!-- 查询用户试用统计信息 -->
    <select id="selectTrialStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN status = 1 THEN 1 END) as validRecords,
            COALESCE(SUM(CASE WHEN status = 1 THEN trial_days ELSE 0 END), 0) as totalTrialDays,
            MIN(start_date) as firstTrialDate,
            MAX(end_date) as lastTrialEndDate,
            COUNT(CASE WHEN end_date >= CURDATE() AND status = 1 THEN 1 END) as currentActiveTrials
        FROM inz_user_trial_log 
        WHERE user_id = #{userId}
    </select>

    <!-- 批量查询用户试用状态 -->
    <select id="selectBatchTrialStatus" resultType="java.util.Map">
        SELECT 
            u.id as userId,
            u.real_name as realName,
            u.phone,
            u.trial_total_days as totalUsedDays,
            u.trial_remaining_days as remainingDays,
            u.try_study as currentTrialFlag,
            t.start_date as latestTrialStart,
            t.end_date as latestTrialEnd,
            CASE 
                WHEN t.end_date >= CURDATE() AND t.status = 1 THEN '试用中'
                WHEN t.end_date &lt; CURDATE() THEN '试用已结束'
                ELSE '未试用'
            END as trialStatus
        FROM inz_user_front u
        LEFT JOIN (
            SELECT 
                user_id,
                start_date,
                end_date,
                status,
                ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) as rn
            FROM inz_user_trial_log 
            WHERE status = 1
        ) t ON u.id = t.user_id AND t.rn = 1
        WHERE u.id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <!-- 查询即将到期的试用用户 -->
    <select id="selectExpiringTrials" resultType="org.jeecg.modules.api.user_trial.entity.InzUserTrialLog">
        SELECT t.*
        FROM inz_user_trial_log t
        WHERE t.status = 1 
          AND t.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL #{days} DAY)
        ORDER BY t.end_date ASC
    </select>

    <!-- 数据一致性检查：查找试用天数不一致的用户 -->
    <select id="selectInconsistentTrialData" resultType="java.util.Map">
        SELECT 
            u.id as userId,
            u.real_name as realName,
            u.trial_total_days as userTableTotal,
            COALESCE(SUM(t.trial_days), 0) as calculatedTotal,
            u.trial_remaining_days as userTableRemaining,
            GREATEST(0, 8 - COALESCE(SUM(t.trial_days), 0)) as calculatedRemaining
        FROM inz_user_front u
        LEFT JOIN inz_user_trial_log t ON u.id = t.user_id AND t.status = 1
        GROUP BY u.id, u.real_name, u.trial_total_days, u.trial_remaining_days
        HAVING u.trial_total_days != COALESCE(SUM(t.trial_days), 0)
            OR u.trial_remaining_days != GREATEST(0, 8 - COALESCE(SUM(t.trial_days), 0))
    </select>

    <!-- 修复数据一致性 -->
    <update id="fixInconsistentTrialData">
        UPDATE inz_user_front u
        SET 
            trial_total_days = (
                SELECT COALESCE(SUM(trial_days), 0) 
                FROM inz_user_trial_log t 
                WHERE t.user_id = u.id AND t.status = 1
            ),
            trial_remaining_days = GREATEST(0, 8 - (
                SELECT COALESCE(SUM(trial_days), 0) 
                FROM inz_user_trial_log t 
                WHERE t.user_id = u.id AND t.status = 1
            )),
            trial_last_update = NOW()
        WHERE u.trial_total_days != (
            SELECT COALESCE(SUM(trial_days), 0) 
            FROM inz_user_trial_log t 
            WHERE t.user_id = u.id AND t.status = 1
        )
    </update>

    <!-- 试用使用情况统计 -->
    <select id="selectTrialUsageStatistics" resultType="java.util.Map">
        SELECT 
            '试用统计' as statType,
            COUNT(DISTINCT u.id) as totalUsers,
            COUNT(DISTINCT CASE WHEN u.trial_total_days > 0 THEN u.id END) as usersWithTrial,
            ROUND(AVG(u.trial_total_days), 2) as avgTrialDaysUsed,
            COUNT(DISTINCT CASE WHEN u.trial_remaining_days = 0 THEN u.id END) as usersExhaustedTrial,
            COUNT(DISTINCT t.user_id) as usersWithTrialRecords,
            COUNT(t.id) as totalTrialRecords,
            COUNT(DISTINCT CASE WHEN t.end_date >= CURDATE() AND t.status = 1 THEN t.user_id END) as currentActiveUsers
        FROM inz_user_front u
        LEFT JOIN inz_user_trial_log t ON u.id = t.user_id AND t.status = 1
    </select>

</mapper>
